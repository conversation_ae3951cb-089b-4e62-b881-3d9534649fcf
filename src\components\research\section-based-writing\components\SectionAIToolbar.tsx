/**
 * Section-Specific AI Toolbar Component
 * Provides contextual AI tools based on the current section being edited
 */

import React, { useState, useEffect } from 'react';
import { 
  Wand2, 
  Quote, 
  PenTool, 
  TrendingUp, 
  Layers, 
  Search,
  Sparkles,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';

import { sectionContextService } from '../services/section-context.service';
import { SECTION_AI_TOOLS, RESEARCH_SECTIONS, CITATION_STYLES } from '../constants';
import { 
  SectionWritingContext, 
  SectionAITool, 
  SectionEnhancementOptions,
  ResearchSection 
} from '../types';

interface SectionAIToolbarProps {
  selectedText?: string;
  onContentInsert: (content: string, mode: 'replace' | 'insert') => void;
  onContentGenerate: (content: string) => void;
  className?: string;
}

export function SectionAIToolbar({
  selectedText,
  onContentInsert,
  onContentGenerate,
  className = ''
}: SectionAIToolbarProps) {
  const [context, setContext] = useState<SectionWritingContext>(sectionContextService.getContext());
  const [isProcessing, setIsProcessing] = useState<string | null>(null);
  const [enhancementOptions, setEnhancementOptions] = useState<SectionEnhancementOptions>({
    includeSearch: true,
    maxSearchResults: 5,
    searchDepth: 'advanced',
    citationStyle: 'APA',
    enhancementType: 'clarity',
    contextFromPreviousSections: true
  });

  useEffect(() => {
    const unsubscribe = sectionContextService.subscribe(setContext);
    return unsubscribe;
  }, []);

  const currentSection = context.currentSection 
    ? RESEARCH_SECTIONS.find(s => s.id === context.currentSection)
    : null;

  const availableTools = SECTION_AI_TOOLS.filter(tool => 
    !currentSection || tool.applicableSections.includes(currentSection.id)
  );

  const handleToolExecution = async (tool: SectionAITool) => {
    if (!currentSection) {
      toast.error('Please select a section first');
      return;
    }

    setIsProcessing(tool.id);

    try {
      const toolContext = sectionContextService.getSectionToolContext(
        currentSection.id, 
        selectedText
      );

      let result: string;

      if (tool.id === 'generate-section') {
        // Generate entire section
        result = await sectionContextService.generateSectionContent(
          currentSection.id,
          enhancementOptions
        );
        onContentGenerate(result);
        toast.success(`${currentSection.name} section generated successfully`);
      } else {
        // Use tool-specific prompt
        const prompt = tool.promptTemplate(toolContext);
        
        // Import the enhanced AI service
        const { enhancedAIService } = await import('../../paper-generator/services/enhanced-ai.service');
        
        result = await enhancedAIService.generateText(prompt, undefined, {
          maxTokens: 1024,
          temperature: 0.7
        });

        // Apply based on tool mode
        switch (tool.mode) {
          case 'replace':
            onContentInsert(result, 'replace');
            break;
          case 'insert':
            onContentInsert(result, 'insert');
            break;
          case 'enhance':
            if (selectedText) {
              onContentInsert(result, 'replace');
            } else {
              onContentInsert(result, 'insert');
            }
            break;
          default:
            onContentGenerate(result);
        }

        toast.success(`${tool.name} completed successfully`);
      }
    } catch (error: any) {
      console.error('Tool execution error:', error);
      toast.error(error.message || `Failed to execute ${tool.name}`);
    } finally {
      setIsProcessing(null);
    }
  };

  const getToolIcon = (iconName: string) => {
    const icons: Record<string, React.ComponentType<any>> = {
      'Wand2': Wand2,
      'Quote': Quote,
      'PenTool': PenTool,
      'TrendingUp': TrendingUp,
      'Layers': Layers,
      'Search': Search,
      'Sparkles': Sparkles,
      'RefreshCw': RefreshCw
    };
    return icons[iconName] || Wand2;
  };

  if (context.writingMode !== 'template' || !currentSection) {
    return null;
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm p-4 ${className}`}>
      <div className="space-y-4">
        {/* Section Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${currentSection.color}`}>
              <currentSection.icon className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{currentSection.name}</h3>
              <p className="text-sm text-gray-600">{currentSection.description}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {currentSection.searchEnabled && (
              <Badge variant="outline" className="text-xs">
                <Search className="h-3 w-3 mr-1" />
                Search Enabled
              </Badge>
            )}
            {currentSection.required && (
              <Badge variant="outline" className="text-xs text-red-600 border-red-200">
                Required
              </Badge>
            )}
          </div>
        </div>

        {/* Enhancement Options */}
        <div className="grid grid-cols-2 gap-3">
          <div>
            <label className="text-xs font-medium text-gray-700 mb-1 block">
              Citation Style
            </label>
            <Select
              value={enhancementOptions.citationStyle}
              onValueChange={(value: any) => 
                setEnhancementOptions(prev => ({ ...prev, citationStyle: value }))
              }
            >
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {CITATION_STYLES.map(style => (
                  <SelectItem key={style.id} value={style.id} className="text-xs">
                    {style.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-xs font-medium text-gray-700 mb-1 block">
              Search Depth
            </label>
            <Select
              value={enhancementOptions.searchDepth}
              onValueChange={(value: any) => 
                setEnhancementOptions(prev => ({ ...prev, searchDepth: value }))
              }
            >
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="basic" className="text-xs">Basic</SelectItem>
                <SelectItem value="advanced" className="text-xs">Advanced</SelectItem>
                <SelectItem value="comprehensive" className="text-xs">Comprehensive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* AI Tools */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700">AI Tools</h4>
          
          <div className="grid grid-cols-2 gap-2">
            {availableTools.map(tool => {
              const IconComponent = getToolIcon(tool.icon);
              const isProcessingTool = isProcessing === tool.id;
              
              return (
                <TooltipProvider key={tool.id}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-auto py-2 px-3 text-xs justify-start"
                        onClick={() => handleToolExecution(tool)}
                        disabled={isProcessingTool || !!isProcessing}
                      >
                        {isProcessingTool ? (
                          <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                        ) : (
                          <IconComponent className="h-3 w-3 mr-2" />
                        )}
                        <span className="truncate">{tool.name}</span>
                        {tool.requiresSearch && currentSection.searchEnabled && (
                          <Search className="h-2 w-2 ml-auto text-blue-500" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom" className="max-w-xs">
                      <p className="text-xs">{tool.description}</p>
                      {tool.requiresSearch && (
                        <p className="text-xs text-blue-600 mt-1">
                          • Includes academic search
                        </p>
                      )}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              );
            })}
          </div>
        </div>

        {/* Section Status */}
        <div className="pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center space-x-2">
              {context.sections[currentSection.id]?.status === 'complete' ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <AlertCircle className="h-3 w-3 text-yellow-500" />
              )}
              <span className="text-gray-600">
                Status: {context.sections[currentSection.id]?.status || 'empty'}
              </span>
            </div>
            
            <div className="text-gray-600">
              {context.sections[currentSection.id]?.wordCount || 0} words
            </div>
          </div>
        </div>

        {/* Context Dependencies */}
        {currentSection.contextDependencies.length > 0 && (
          <div className="pt-2 border-t border-gray-100">
            <div className="text-xs text-gray-600 mb-2">
              References: {currentSection.contextDependencies.join(', ')}
            </div>
            <div className="flex items-center space-x-1">
              {currentSection.contextDependencies.map(depId => {
                const depSection = RESEARCH_SECTIONS.find(s => s.id === depId);
                const hasContent = context.sections[depId]?.content;
                return (
                  <Badge 
                    key={depId} 
                    variant="outline" 
                    className={`text-xs ${hasContent ? 'border-green-200 text-green-700' : 'border-gray-200 text-gray-500'}`}
                  >
                    {depSection?.name || depId}
                  </Badge>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
