/**
 * Section Context Management Service
 * Manages section-based writing state, context, and AI integration
 */

import { 
  SectionWritingContext, 
  SectionContent, 
  ResearchSection, 
  SectionProgress,
  SectionEnhancementOptions,
  SectionToolContext
} from '../types';
import { RESEARCH_SECTIONS, SECTION_WORD_TARGETS } from '../constants';
import { enhancedAIService } from '../../paper-generator/services/enhanced-ai.service';
import { tavilySearchService } from '../../ai-tutor/services/tavily-search.service';

class SectionContextService {
  private context: SectionWritingContext = {
    currentSection: null,
    sections: {},
    paperMetadata: {
      title: '',
      abstract: '',
      keywords: [],
      researchField: '',
      authors: []
    },
    writingMode: 'freeform',
    showSectionNavigation: false
  };

  private listeners: Array<(context: SectionWritingContext) => void> = [];

  /**
   * Initialize section-based writing mode
   */
  initializeSectionMode(paperTitle?: string, researchField?: string): void {
    this.context = {
      ...this.context,
      writingMode: 'template',
      showSectionNavigation: true,
      paperMetadata: {
        ...this.context.paperMetadata,
        title: paperTitle || '',
        researchField: researchField || ''
      },
      sections: this.initializeEmptySections()
    };
    this.notifyListeners();
  }

  /**
   * Initialize empty sections
   */
  private initializeEmptySections(): Record<string, SectionContent> {
    const sections: Record<string, SectionContent> = {};
    
    RESEARCH_SECTIONS.forEach(section => {
      sections[section.id] = {
        id: section.id,
        content: '',
        wordCount: 0,
        lastModified: new Date(),
        status: 'empty',
        aiGenerated: false,
        citations: [],
        metadata: {}
      };
    });

    return sections;
  }

  /**
   * Set current active section
   */
  setCurrentSection(sectionId: string): void {
    if (this.context.sections[sectionId]) {
      this.context.currentSection = sectionId;
      this.notifyListeners();
    }
  }

  /**
   * Update section content
   */
  updateSectionContent(sectionId: string, content: string, aiGenerated = false): void {
    if (!this.context.sections[sectionId]) return;

    const wordCount = content.trim().split(/\s+/).filter(word => word.length > 0).length;
    const status = this.determineSectionStatus(sectionId, wordCount);

    this.context.sections[sectionId] = {
      ...this.context.sections[sectionId],
      content,
      wordCount,
      lastModified: new Date(),
      status,
      aiGenerated
    };

    this.notifyListeners();
  }

  /**
   * Determine section status based on content and word count
   */
  private determineSectionStatus(sectionId: string, wordCount: number): SectionContent['status'] {
    const target = SECTION_WORD_TARGETS[sectionId as keyof typeof SECTION_WORD_TARGETS];
    if (!target) return 'draft';

    if (wordCount === 0) return 'empty';
    if (wordCount < target.min) return 'draft';
    if (wordCount >= target.target) return 'complete';
    return 'review';
  }

  /**
   * Get section context for AI tools
   */
  getSectionToolContext(sectionId: string, selectedText?: string): SectionToolContext {
    const section = RESEARCH_SECTIONS.find(s => s.id === sectionId);
    if (!section) throw new Error(`Section ${sectionId} not found`);

    const sectionContent = this.context.sections[sectionId]?.content || '';
    const previousSections: Record<string, string> = {};

    // Get content from dependency sections
    section.contextDependencies.forEach(depId => {
      if (this.context.sections[depId]?.content) {
        previousSections[depId] = this.context.sections[depId].content;
      }
    });

    return {
      currentSection: section,
      sectionContent,
      previousSections,
      paperMetadata: this.context.paperMetadata,
      selectedText
    };
  }

  /**
   * Generate section content using AI
   */
  async generateSectionContent(
    sectionId: string, 
    options: SectionEnhancementOptions = {
      includeSearch: true,
      maxSearchResults: 5,
      searchDepth: 'advanced',
      citationStyle: 'APA',
      enhancementType: 'clarity',
      contextFromPreviousSections: true
    }
  ): Promise<string> {
    const toolContext = this.getSectionToolContext(sectionId);
    const section = toolContext.currentSection;

    let searchResults: any[] = [];

    // Perform search if enabled for this section
    if (section.searchEnabled && options.includeSearch && tavilySearchService.isAvailable()) {
      const searchQuery = this.buildSearchQuery(sectionId, toolContext);
      
      try {
        const searchResponse = await tavilySearchService.searchAcademic(searchQuery, {
          maxResults: options.maxSearchResults,
          searchDepth: options.searchDepth,
          includeAnswer: true
        });
        searchResults = searchResponse.results;
      } catch (error) {
        console.warn('Search failed, proceeding without search results:', error);
      }
    }

    // Build AI prompt with context
    const prompt = this.buildAIPrompt(sectionId, toolContext, searchResults, options);

    // Generate content using AI
    const generatedContent = await enhancedAIService.generateText(prompt, undefined, {
      maxTokens: this.getTokenLimitForSection(sectionId),
      temperature: 0.7
    });

    // Update section with generated content
    this.updateSectionContent(sectionId, generatedContent, true);

    // Store metadata
    this.context.sections[sectionId].metadata = {
      ...this.context.sections[sectionId].metadata,
      generationPrompt: prompt,
      searchResults,
      aiModel: enhancedAIService.getDefaultModel()
    };

    return generatedContent;
  }

  /**
   * Build search query for section
   */
  private buildSearchQuery(sectionId: string, context: SectionToolContext): string {
    const { paperMetadata } = context;
    const baseQuery = `${paperMetadata.title} ${paperMetadata.researchField}`;
    
    switch (sectionId) {
      case 'introduction':
        return `${baseQuery} literature review background research`;
      case 'methodology':
        return `${baseQuery} research methods methodology techniques`;
      case 'discussion':
        return `${baseQuery} analysis implications findings discussion`;
      default:
        return `${baseQuery} academic research ${sectionId}`;
    }
  }

  /**
   * Build AI prompt with context and search results
   */
  private buildAIPrompt(
    sectionId: string, 
    context: SectionToolContext, 
    searchResults: any[], 
    options: SectionEnhancementOptions
  ): string {
    const { currentSection, previousSections, paperMetadata } = context;
    
    let prompt = currentSection.aiPromptTemplate
      .replace('{title}', paperMetadata.title)
      .replace('{researchField}', paperMetadata.researchField)
      .replace('{citationStyle}', options.citationStyle);

    // Add context from previous sections
    if (options.contextFromPreviousSections && Object.keys(previousSections).length > 0) {
      prompt += '\n\nContext from previous sections:\n';
      Object.entries(previousSections).forEach(([id, content]) => {
        prompt += `\n${id.toUpperCase()}:\n${content.slice(0, 500)}...\n`;
      });
    }

    // Add search results if available
    if (searchResults.length > 0) {
      prompt += '\n\nRelevant research findings to reference:\n';
      searchResults.forEach((result, index) => {
        prompt += `\n[${index + 1}] ${result.title}\n${result.content.slice(0, 300)}...\nSource: ${result.url}\n`;
      });
      prompt += '\nPlease incorporate relevant information from these sources with proper citations.';
    }

    prompt += `\n\nTarget word count: ${SECTION_WORD_TARGETS[sectionId as keyof typeof SECTION_WORD_TARGETS]?.target || 500} words.`;
    prompt += '\n\nWrite in academic style with proper structure and citations where appropriate.';

    return prompt;
  }

  /**
   * Get token limit for section based on expected length
   */
  private getTokenLimitForSection(sectionId: string): number {
    const target = SECTION_WORD_TARGETS[sectionId as keyof typeof SECTION_WORD_TARGETS];
    if (!target) return 1024;
    
    // Rough conversion: 1 token ≈ 0.75 words, add buffer for formatting
    return Math.ceil((target.max * 1.5) / 0.75);
  }

  /**
   * Get writing progress
   */
  getProgress(): SectionProgress {
    const totalSections = RESEARCH_SECTIONS.length;
    const completedSections = Object.values(this.context.sections)
      .filter(section => section.status === 'complete').length;
    
    const currentWordCount = Object.values(this.context.sections)
      .reduce((total, section) => total + section.wordCount, 0);
    
    const targetWordCount = RESEARCH_SECTIONS
      .reduce((total, section) => {
        const target = SECTION_WORD_TARGETS[section.id as keyof typeof SECTION_WORD_TARGETS];
        return total + (target?.target || 0);
      }, 0);

    return {
      totalSections,
      completedSections,
      currentSection: this.context.currentSection,
      estimatedTimeRemaining: this.estimateTimeRemaining(completedSections, totalSections),
      wordCountProgress: {
        current: currentWordCount,
        target: targetWordCount
      }
    };
  }

  /**
   * Estimate time remaining based on progress
   */
  private estimateTimeRemaining(completed: number, total: number): number {
    if (completed === 0) return 120; // 2 hours estimate for full paper
    const remainingSections = total - completed;
    const avgTimePerSection = 15; // 15 minutes per section estimate
    return remainingSections * avgTimePerSection;
  }

  /**
   * Get current context
   */
  getContext(): SectionWritingContext {
    return { ...this.context };
  }

  /**
   * Subscribe to context changes
   */
  subscribe(listener: (context: SectionWritingContext) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Notify all listeners of context changes
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.context));
  }

  /**
   * Reset to freeform mode
   */
  exitSectionMode(): void {
    this.context = {
      ...this.context,
      writingMode: 'freeform',
      showSectionNavigation: false,
      currentSection: null
    };
    this.notifyListeners();
  }

  /**
   * Update paper metadata
   */
  updatePaperMetadata(metadata: Partial<SectionWritingContext['paperMetadata']>): void {
    this.context.paperMetadata = {
      ...this.context.paperMetadata,
      ...metadata
    };
    this.notifyListeners();
  }
}

export const sectionContextService = new SectionContextService();
