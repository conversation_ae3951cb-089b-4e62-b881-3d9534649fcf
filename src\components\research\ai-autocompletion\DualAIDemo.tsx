/**
 * Dual AI Demo Component
 * Showcases both inline autocompletion and AI suggestion panel features
 */

import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  <PERSON><PERSON><PERSON>, 
  Zap, 
  Brain, 
  BookOpen, 
  Settings,
  Play,
  Pause,
  RotateCcw,
  ArrowRight,
  CheckCircle,
  Info
} from 'lucide-react';
import { DualAIEditor } from './DualAIEditor';
import { autocompletionUtils } from './index';

export function DualAIDemo() {
  const [content, setContent] = useState(`# Dual AI Writing Assistant Demo

Welcome to the enhanced writing experience with two distinct AI features:

## 1. Real-time Autocompletion ⚡
Start typing a sentence and watch as AI suggests completions in real-time. The suggestions appear as ghost text that you can accept with Tab or reject with Esc.

## 2. AI Suggestion Panel 🧠  
Click the floating AI button to get paragraph-level suggestions. Perfect for expanding ideas, adding research, or continuing your narrative.

## Try These Examples:

**For Autocompletion**: Start typing "The future of artificial intelligence in education will"

**For AI Suggestions**: Click the AI button and ask for "a paragraph about the benefits of AI in learning"

---

Start writing below to experience both features:

`);

  const [inlineEnabled, setInlineEnabled] = useState(true);
  const [suggestionsEnabled, setSuggestionsEnabled] = useState(true);
  const [showStats, setShowStats] = useState(true);

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
  };

  const resetDemo = () => {
    setContent(`# Dual AI Writing Assistant Demo

Welcome to the enhanced writing experience! Start typing below to see both features in action:

## Your Writing Space

`);
  };

  // Initialize styles when component mounts
  React.useEffect(() => {
    autocompletionUtils.initializeStyles();
    
    return () => {
      autocompletionUtils.cleanupStyles();
    };
  }, []);

  const wordCount = autocompletionUtils.extractTextFromHTML(content).split(/\s+/).length;
  const readingTime = autocompletionUtils.calculateReadingTime(content);

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <Card className="modern-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl">
                <Sparkles className="h-8 w-8 text-white" />
              </div>
              <div>
                <CardTitle className="text-3xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Dual AI Writing Assistant
                </CardTitle>
                <p className="text-gray-600 mt-2">
                  Experience the perfect combination of real-time autocompletion and intelligent paragraph suggestions
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={resetDemo}
                className="flex items-center gap-2 smooth-transition"
              >
                <RotateCcw className="h-4 w-4" />
                Reset Demo
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Feature Overview */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card className="modern-card">
          <CardContent className="pt-6">
            <div className="flex items-start gap-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Zap className="h-6 w-6 text-blue-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-lg mb-2">Real-time Autocompletion</h3>
                <p className="text-gray-600 text-sm mb-3">
                  AI completes your sentences as you type. Suggestions appear as ghost text that you can quickly accept or reject.
                </p>
                <div className="flex items-center gap-2">
                  <Badge variant={inlineEnabled ? "default" : "secondary"} className="text-xs">
                    {inlineEnabled ? 'Active' : 'Disabled'}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setInlineEnabled(!inlineEnabled)}
                    className="h-6 px-2 text-xs"
                  >
                    {inlineEnabled ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
                  </Button>
                </div>
                <div className="mt-3 p-2 bg-blue-50 rounded text-xs text-blue-700">
                  <strong>How to use:</strong> Start typing a sentence • Press Tab to accept • Press Esc to reject
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="modern-card">
          <CardContent className="pt-6">
            <div className="flex items-start gap-4">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Brain className="h-6 w-6 text-purple-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-lg mb-2">AI Suggestion Panel</h3>
                <p className="text-gray-600 text-sm mb-3">
                  Get paragraph-level suggestions with research integration. Perfect for expanding ideas and adding depth.
                </p>
                <div className="flex items-center gap-2">
                  <Badge variant={suggestionsEnabled ? "default" : "secondary"} className="text-xs">
                    {suggestionsEnabled ? 'Active' : 'Disabled'}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSuggestionsEnabled(!suggestionsEnabled)}
                    className="h-6 px-2 text-xs"
                  >
                    {suggestionsEnabled ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
                  </Button>
                </div>
                <div className="mt-3 p-2 bg-purple-50 rounded text-xs text-purple-700">
                  <strong>How to use:</strong> Click the floating AI button • Choose from generated suggestions • Add custom prompts
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Stats Panel */}
      {showStats && (
        <Card className="modern-card">
          <CardContent className="pt-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-1">{wordCount}</div>
                <div className="text-sm text-gray-600">Words Written</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-1">{readingTime}</div>
                <div className="text-sm text-gray-600">Min Read</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-1">
                  {inlineEnabled ? 'ON' : 'OFF'}
                </div>
                <div className="text-sm text-gray-600">Autocompletion</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600 mb-1">
                  {suggestionsEnabled ? 'ON' : 'OFF'}
                </div>
                <div className="text-sm text-gray-600">AI Suggestions</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card className="modern-card">
        <CardContent className="pt-6">
          <h3 className="font-semibold mb-4 flex items-center gap-2">
            <Info className="h-5 w-5 text-blue-600" />
            Quick Start Guide
          </h3>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">1</div>
                <h4 className="font-medium">Start Typing</h4>
              </div>
              <p className="text-sm text-gray-600 ml-8">
                Begin writing a sentence and watch for ghost text suggestions to appear automatically.
              </p>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-purple-600 text-white rounded-full flex items-center justify-center text-xs font-bold">2</div>
                <h4 className="font-medium">Use AI Suggestions</h4>
              </div>
              <p className="text-sm text-gray-600 ml-8">
                Click the floating AI button to get paragraph-level suggestions and research integration.
              </p>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs font-bold">3</div>
                <h4 className="font-medium">Accept or Reject</h4>
              </div>
              <p className="text-sm text-gray-600 ml-8">
                Use Tab/Esc for autocompletion or click accept/reject buttons for AI suggestions.
              </p>
            </div>
          </div>

          <Separator className="my-6" />

          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Zap className="h-4 w-4 text-blue-600" />
                Autocompletion Tips
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                  Works best with incomplete sentences
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                  Learns from your writing style
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                  Pauses after rejections to avoid interruption
                </li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Brain className="h-4 w-4 text-purple-600" />
                AI Suggestions Tips
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                  Provides research-backed content
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                  Supports custom prompts
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                  Automatically manages citations
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Editor */}
      <Card className="modern-card">
        <CardContent className="p-0">
          <DualAIEditor
            content={content}
            onChange={handleContentChange}
            enableInlineAutocompletion={inlineEnabled}
            enableAISuggestions={suggestionsEnabled}
            className="min-h-[600px]"
          />
        </CardContent>
      </Card>

      {/* Footer */}
      <Card className="modern-card">
        <CardContent className="pt-6">
          <div className="text-center">
            <h3 className="font-semibold mb-2">🎉 Experience the Future of Writing</h3>
            <p className="text-gray-600 text-sm">
              This demo showcases how AI can enhance your writing workflow without being intrusive. 
              Both features work together to provide a seamless, intelligent writing experience.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
