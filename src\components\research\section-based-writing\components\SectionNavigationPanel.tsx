/**
 * Section Navigation Panel Component
 * Provides navigation and progress tracking for section-based writing
 */

import React, { useState, useEffect } from 'react';
import {
  ChevronRight,
  ChevronDown,
  ChevronLeft,
  CheckCircle,
  Circle,
  Clock,
  FileText,
  Target,
  TrendingUp,
  Settings,
  X
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

import { sectionContextService } from '../services/section-context.service';
import { RESEARCH_SECTIONS, SECTION_WORD_TARGETS } from '../constants';
import { 
  SectionWritingContext, 
  SectionProgress, 
  ResearchSection,
  SectionContent 
} from '../types';

interface SectionNavigationPanelProps {
  isVisible: boolean;
  onToggleVisibility: () => void;
  onSectionSelect: (sectionId: string) => void;
  className?: string;
}

export function SectionNavigationPanel({
  isVisible,
  onToggleVisibility,
  onSectionSelect,
  className = ''
}: SectionNavigationPanelProps) {
  const [context, setContext] = useState<SectionWritingContext>(sectionContextService.getContext());
  const [progress, setProgress] = useState<SectionProgress>(sectionContextService.getProgress());
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [isMinimized, setIsMinimized] = useState(false);

  useEffect(() => {
    const unsubscribe = sectionContextService.subscribe((newContext) => {
      setContext(newContext);
      setProgress(sectionContextService.getProgress());
    });

    return unsubscribe;
  }, []);

  const toggleSectionExpanded = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const getSectionStatus = (section: ResearchSection): SectionContent['status'] => {
    return context.sections[section.id]?.status || 'empty';
  };

  const getSectionWordCount = (section: ResearchSection): number => {
    return context.sections[section.id]?.wordCount || 0;
  };

  const getStatusIcon = (status: SectionContent['status']) => {
    switch (status) {
      case 'complete':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'review':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'draft':
        return <Circle className="h-4 w-4 text-blue-500" />;
      default:
        return <Circle className="h-4 w-4 text-gray-300" />;
    }
  };

  const getStatusColor = (status: SectionContent['status']) => {
    switch (status) {
      case 'complete':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'draft':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  const getWordCountProgress = (section: ResearchSection): number => {
    const wordCount = getSectionWordCount(section);
    const target = SECTION_WORD_TARGETS[section.id as keyof typeof SECTION_WORD_TARGETS];
    if (!target) return 0;
    return Math.min((wordCount / target.target) * 100, 100);
  };

  // Show floating toggle when panel is hidden
  if (!isVisible) {
    return (
      <div className="fixed right-4 top-1/2 transform -translate-y-1/2 z-50">
        <Button
          onClick={onToggleVisibility}
          className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg rounded-full h-12 w-12 p-0"
        >
          <FileText className="h-5 w-5" />
        </Button>
      </div>
    );
  }

  return (
    <div className={`fixed right-0 top-0 h-full ${isMinimized ? 'w-16' : 'w-80'} bg-white border-l border-gray-200 shadow-lg z-50 transition-all duration-300 ${className}`}>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-blue-600" />
            {!isMinimized && <h3 className="font-semibold text-gray-900">Research Sections</h3>}
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(!isMinimized)}
              className="h-8 w-8 p-0"
            >
              {isMinimized ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleVisibility}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Progress Overview */}
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Overall Progress</span>
              <span className="text-sm text-gray-600">
                {progress.completedSections}/{progress.totalSections} sections
              </span>
            </div>
            <Progress 
              value={(progress.completedSections / progress.totalSections) * 100} 
              className="h-2"
            />
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center space-x-2">
                <Target className="h-4 w-4 text-blue-500" />
                <div>
                  <div className="font-medium text-gray-700">Word Count</div>
                  <div className="text-gray-600">
                    {progress.wordCountProgress.current.toLocaleString()} / {progress.wordCountProgress.target.toLocaleString()}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-green-500" />
                <div>
                  <div className="font-medium text-gray-700">Est. Time</div>
                  <div className="text-gray-600">{progress.estimatedTimeRemaining}m</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Section List */}
        <ScrollArea className="flex-1">
          <div className="p-2">
            {RESEARCH_SECTIONS.map((section) => {
              const status = getSectionStatus(section);
              const wordCount = getSectionWordCount(section);
              const isExpanded = expandedSections.has(section.id);
              const isActive = context.currentSection === section.id;
              const wordProgress = getWordCountProgress(section);
              const target = SECTION_WORD_TARGETS[section.id as keyof typeof SECTION_WORD_TARGETS];

              if (isMinimized) {
                return (
                  <TooltipProvider key={section.id}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div
                          className={`
                            flex items-center justify-center p-2 mb-2 rounded-lg cursor-pointer transition-all
                            ${isActive
                              ? 'bg-blue-50 border border-blue-200'
                              : 'hover:bg-gray-50'
                            }
                          `}
                          onClick={() => onSectionSelect(section.id)}
                        >
                          <div className={`p-1 rounded ${section.color}`}>
                            <section.icon className="h-3 w-3 text-white" />
                          </div>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="left">
                        <div className="text-xs">
                          <div className="font-medium">{section.name}</div>
                          <div className="text-gray-600">{wordCount} words</div>
                          <div className="text-gray-600">Status: {status}</div>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                );
              }

              return (
                <div key={section.id} className="mb-2">
                  <div
                    className={`
                      flex items-center p-3 rounded-lg border cursor-pointer transition-all
                      ${isActive 
                        ? 'bg-blue-50 border-blue-200 shadow-sm' 
                        : 'bg-white border-gray-200 hover:bg-gray-50'
                      }
                    `}
                    onClick={() => onSectionSelect(section.id)}
                  >
                    <div className="flex items-center space-x-3 flex-1">
                      <div className={`p-2 rounded-lg ${section.color}`}>
                        <section.icon className="h-4 w-4 text-white" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-gray-900 truncate">
                            {section.name}
                          </h4>
                          {getStatusIcon(status)}
                        </div>
                        
                        <div className="flex items-center justify-between mt-1">
                          <p className="text-xs text-gray-600 truncate">
                            {section.description}
                          </p>
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${getStatusColor(status)}`}
                          >
                            {status}
                          </Badge>
                        </div>

                        {/* Word count progress */}
                        {target && (
                          <div className="mt-2">
                            <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                              <span>{wordCount} words</span>
                              <span>{target.target} target</span>
                            </div>
                            <Progress value={wordProgress} className="h-1" />
                          </div>
                        )}
                      </div>
                    </div>

                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 ml-2"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleSectionExpanded(section.id);
                      }}
                    >
                      {isExpanded ? (
                        <ChevronDown className="h-3 w-3" />
                      ) : (
                        <ChevronRight className="h-3 w-3" />
                      )}
                    </Button>
                  </div>

                  {/* Expanded section details */}
                  {isExpanded && (
                    <div className="ml-4 mt-2 p-3 bg-gray-50 rounded-lg border border-gray-200">
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center justify-between">
                          <span className="font-medium text-gray-700">Details</span>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <Settings className="h-3 w-3 text-gray-400" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Section configuration</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                          <div>Required: {section.required ? 'Yes' : 'No'}</div>
                          <div>Order: {section.order}</div>
                          <div>Search: {section.searchEnabled ? 'Yes' : 'No'}</div>
                          <div>Est. Words: {section.estimatedWords}</div>
                        </div>

                        {section.contextDependencies.length > 0 && (
                          <div>
                            <div className="font-medium text-gray-700 mb-1">Dependencies:</div>
                            <div className="flex flex-wrap gap-1">
                              {section.contextDependencies.map(depId => (
                                <Badge key={depId} variant="outline" className="text-xs">
                                  {RESEARCH_SECTIONS.find(s => s.id === depId)?.name || depId}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </ScrollArea>

        {/* Footer Actions */}
        {!isMinimized && (
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <div className="space-y-2">
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => {
                  // TODO: Implement export functionality
                  console.log('Export paper');
                }}
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Export Paper
              </Button>

              <div className="text-xs text-gray-600 text-center">
                {context.paperMetadata.title || 'Untitled Research Paper'}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
