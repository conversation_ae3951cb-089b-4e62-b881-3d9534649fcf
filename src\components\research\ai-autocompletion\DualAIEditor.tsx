/**
 * Dual AI Editor Component
 * Combines inline autocompletion and AI suggestion panel as separate, distinct features
 */

import React, { useState, useEffect, useCallback, useRef, forwardRef, useImperativeHandle } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Underline } from '@tiptap/extension-underline';
import { TextStyle } from '@tiptap/extension-text-style';
import { FontFamily } from '@tiptap/extension-font-family';
import { Highlight } from '@tiptap/extension-highlight';
import { Color } from '@tiptap/extension-color';
import { BulletList } from '@tiptap/extension-bullet-list';
import { OrderedList } from '@tiptap/extension-ordered-list';
import { ListItem } from '@tiptap/extension-list-item';
import { Table } from '@tiptap/extension-table';
import { TableRow } from '@tiptap/extension-table-row';
import { TableCell } from '@tiptap/extension-table-cell';
import { TableHeader } from '@tiptap/extension-table-header';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import Typography from '@tiptap/extension-typography';
import Placeholder from '@tiptap/extension-placeholder';
import { ResizableImage } from 'tiptap-extension-resizable-image';
import ImageAlignment from '../paper-generator/extensions/image-alignment';
import { InlineAutocompletion } from './InlineAutocompletion';
import { AISuggestionPanel } from './AISuggestionPanel';
import { CitationManager } from './CitationManager';
import { CitationReference } from './ai-autocompletion.service';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Toggle } from '@/components/ui/toggle';
import { 
  Zap, 
  Brain, 
  Settings,
  Quote
} from 'lucide-react';
import { cn } from '@/lib/utils';
import '../paper-generator/editor-styles.css';

export interface DualAIEditorRef {
  editor: any;
  insertContent: (content: string) => void;
  getSelectedText: () => string;
  getSelectionRange: () => { from: number; to: number } | null;
  replaceSelectedText: (text: string) => void;
  insertContentAt: (position: number, content: string) => void;
  getCursorPosition: () => number;
  getEditorElement: () => HTMLElement | null;
}

interface DualAIEditorProps {
  content: string;
  onChange: (content: string) => void;
  onSelectionChange?: () => void;
  className?: string;
  editable?: boolean;
  enableInlineAutocompletion?: boolean;
  enableAISuggestions?: boolean;
  citationStyle?: 'APA' | 'MLA';
}

export const DualAIEditor = forwardRef<DualAIEditorRef, DualAIEditorProps>(
  ({ 
    content, 
    onChange, 
    onSelectionChange, 
    className = '', 
    editable = true,
    enableInlineAutocompletion = true,
    enableAISuggestions = true,
    citationStyle = 'APA'
  }, ref) => {
    // State management
    const [cursorPosition, setCursorPosition] = useState(0);
    const [citations, setCitations] = useState<CitationReference[]>([]);
    const [showCitationManager, setShowCitationManager] = useState(false);
    const [showAISuggestionPanel, setShowAISuggestionPanel] = useState(false);
    const [inlineAutocompletionEnabled, setInlineAutocompletionEnabled] = useState(enableInlineAutocompletion);
    const [aiSuggestionsEnabled, setAiSuggestionsEnabled] = useState(enableAISuggestions);
    const [currentCitationStyle, setCurrentCitationStyle] = useState<'APA' | 'MLA'>(citationStyle);
    
    // Refs
    const editorElementRef = useRef<HTMLElement | null>(null);

    // Convert basic markdown to HTML for initial rendering
    const convertMarkdownToHTML = useCallback((markdown: string): string => {
      let html = markdown
        // Headers
        .replace(/^# (.+)$/gm, '<h1>$1</h1>')
        .replace(/^## (.+)$/gm, '<h2>$1</h2>')
        .replace(/^### (.+)$/gm, '<h3>$1</h3>')
        // Bold & Italic
        .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.+?)\*/g, '<em>$1</em>')
        // Lists
        .replace(/^\- (.+)$/gm, '<ul><li>$1</li></ul>')
        .replace(/^\d+\. (.+)$/gm, '<ol><li>$1</li></ol>')
        // Fix nested lists
        .replace(/<\/ul>\n<ul>/g, '')
        .replace(/<\/ol>\n<ol>/g, '')
        // Paragraphs
        .replace(/^(?!<[oh][lu]>|<li>|<h[1-6]>)(.+)$/gm, '<p>$1</p>')
        // Fix empty lines
        .replace(/<p><\/p>/g, '<p><br></p>');

      return html;
    }, []);

    // Initialize editor
    const editor = useEditor({
      extensions: [
        StarterKit.configure({
          heading: {
            levels: [1, 2, 3, 4]
          },
          bulletList: false,
          orderedList: false,
        }),
        BulletList.configure({
          HTMLAttributes: {
            class: 'rich-editor-bullet-list',
          },
        }),
        OrderedList.configure({
          HTMLAttributes: {
            class: 'rich-editor-ordered-list',
          },
        }),
        ListItem.configure({
          HTMLAttributes: {
            class: 'rich-editor-list-item',
          },
        }),
        Underline,
        TextStyle,
        FontFamily.configure({
          types: ['textStyle'],
        }),
        Highlight,
        Color,
        ResizableImage.configure({
          allowBase64: true,
        }),
        ImageAlignment.configure({
          types: ['resizableImage'],
        }),
        Table.configure({
          resizable: true,
          handleWidth: 5,
          cellMinWidth: 25,
        }),
        TableRow,
        TableCell.configure({
          HTMLAttributes: {
            style: 'border: 1px solid #e5e7eb; padding: 8px; vertical-align: top;'
          }
        }),
        TableHeader.configure({
          HTMLAttributes: {
            style: 'border: 1px solid #e5e7eb; padding: 8px; background-color: #f9fafb; font-weight: bold;'
          }
        }),
        Link.configure({
          openOnClick: false,
        }),
        TextAlign.configure({
          types: ['heading', 'paragraph'],
        }),
        Typography,
        Placeholder.configure({
          placeholder: ({ node }) => {
            if (node.type.name === 'heading') {
              return 'What\'s the title?'
            }
            return 'Start writing... AI will help complete your sentences and suggest paragraphs.';
          },
        }),
      ],
      content: convertMarkdownToHTML(content),
      editable,
      editorProps: {
        attributes: {
          class: 'dual-ai-editor-content',
        },
      },
      onUpdate: ({ editor }) => {
        const html = editor.getHTML();
        onChange(html);
        
        // Update cursor position
        const { from } = editor.state.selection;
        setCursorPosition(from);
      },
      onSelectionUpdate: ({ editor }) => {
        // Update cursor position
        const { from } = editor.state.selection;
        setCursorPosition(from);
        
        // Call the selection change handler if provided
        if (onSelectionChange) {
          onSelectionChange();
        }
      },
      onCreate: ({ editor }) => {
        // Store reference to editor element
        editorElementRef.current = editor.view.dom as HTMLElement;
      },
    });

    // Handle content insertion for autocompletion
    const handleContentInsert = useCallback((content: string, position?: number) => {
      if (!editor) return;
      
      try {
        if (position !== undefined) {
          // Insert content at the specified position
          editor.commands.insertContentAt(position, content);
          
          // Move cursor to end of inserted text
          const newPosition = position + content.length;
          editor.commands.setTextSelection(newPosition);
          setCursorPosition(newPosition);
        } else {
          // Insert at current cursor position
          editor.commands.insertContent(content);
          const newPosition = cursorPosition + content.length;
          setCursorPosition(newPosition);
        }
      } catch (error) {
        console.error('Error inserting content:', error);
      }
    }, [editor, cursorPosition]);

    // Handle inline autocompletion acceptance
    const handleInlineCompletionAccept = useCallback((completion: string) => {
      if (!editor) return;
      
      try {
        // Insert the completion at current cursor position
        editor.commands.insertContent(completion);
        
        // Update cursor position
        const newPosition = cursorPosition + completion.length;
        setCursorPosition(newPosition);
      } catch (error) {
        console.error('Error accepting completion:', error);
      }
    }, [editor, cursorPosition]);

    // Handle citation addition
    const handleCitationAdd = useCallback((citation: CitationReference) => {
      setCitations(prev => {
        // Check if citation already exists
        const exists = prev.some(c => c.url === citation.url);
        if (exists) return prev;
        
        return [...prev, citation];
      });
      
      // Show citation manager if not visible
      if (!showCitationManager) {
        setShowCitationManager(true);
      }
    }, [showCitationManager]);

    // Handle citation removal
    const handleCitationRemove = useCallback((citationId: string) => {
      setCitations(prev => prev.filter(c => c.id !== citationId));
    }, []);

    // Handle citation update
    const handleCitationUpdate = useCallback((updatedCitation: CitationReference) => {
      setCitations(prev => prev.map(c => 
        c.id === updatedCitation.id ? updatedCitation : c
      ));
    }, []);

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      editor,
      
      insertContent: (content: string) => {
        editor?.commands.insertContent(content);
      },
      
      getSelectedText: () => {
        return editor?.state.doc.textBetween(
          editor.state.selection.from,
          editor.state.selection.to,
          ' '
        ) || '';
      },
      
      getSelectionRange: () => {
        if (!editor?.state.selection) return null;
        return {
          from: editor.state.selection.from,
          to: editor.state.selection.to
        };
      },
      
      replaceSelectedText: (text: string) => {
        if (!editor) return;
        const { from, to } = editor.state.selection;
        editor.commands.insertContentAt({ from, to }, text);
      },
      
      insertContentAt: (position: number, content: string) => {
        editor?.commands.insertContentAt(position, content);
      },
      
      getCursorPosition: () => {
        return editor?.state.selection.from || 0;
      },
      
      getEditorElement: () => {
        return editorElementRef.current;
      }
    }), [editor]);

    // Update content when prop changes
    useEffect(() => {
      if (editor && content !== editor.getHTML()) {
        editor.commands.setContent(convertMarkdownToHTML(content));
      }
    }, [content, editor, convertMarkdownToHTML]);

    if (!editor) {
      return <div className="p-4 text-center text-gray-500">Loading editor...</div>;
    }

    return (
      <div className={cn("relative", className)}>
        {/* AI Features Control Bar */}
        <div className="flex items-center justify-between p-3 border-b bg-gray-50">
          <div className="flex items-center gap-3">
            <h3 className="text-sm font-medium text-gray-700">AI Features</h3>
            <div className="flex items-center gap-2">
              <Toggle
                pressed={inlineAutocompletionEnabled}
                onPressedChange={setInlineAutocompletionEnabled}
                size="sm"
                className="h-8"
              >
                <Zap className="h-3 w-3 mr-1" />
                <span className="text-xs">Auto-complete</span>
              </Toggle>
              <Toggle
                pressed={aiSuggestionsEnabled}
                onPressedChange={setAiSuggestionsEnabled}
                size="sm"
                className="h-8"
              >
                <Brain className="h-3 w-3 mr-1" />
                <span className="text-xs">AI Suggestions</span>
              </Toggle>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {citations.length} citations
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowCitationManager(!showCitationManager)}
              className="h-8 px-2"
            >
              <Quote className="h-3 w-3 mr-1" />
              <span className="text-xs">Citations</span>
            </Button>
          </div>
        </div>

        {/* Main Editor */}
        <div className="relative">
          <EditorContent 
            editor={editor} 
            className="min-h-[400px] focus-within:outline-none p-6"
          />
          
          {/* Inline Autocompletion */}
          {inlineAutocompletionEnabled && (
            <InlineAutocompletion
              content={editor.getHTML()}
              cursorPosition={cursorPosition}
              onAcceptCompletion={handleInlineCompletionAccept}
              isEnabled={inlineAutocompletionEnabled}
              editorElement={editorElementRef.current}
            />
          )}
        </div>
        
        {/* AI Suggestion Panel */}
        {aiSuggestionsEnabled && (
          <AISuggestionPanel
            content={editor.getHTML()}
            cursorPosition={cursorPosition}
            onContentInsert={handleContentInsert}
            onCitationAdd={handleCitationAdd}
            isVisible={showAISuggestionPanel}
            onToggleVisibility={() => setShowAISuggestionPanel(!showAISuggestionPanel)}
          />
        )}
        
        {/* Citation Manager */}
        <CitationManager
          citations={citations}
          onCitationRemove={handleCitationRemove}
          onCitationUpdate={handleCitationUpdate}
          citationStyle={currentCitationStyle}
          onStyleChange={setCurrentCitationStyle}
          isVisible={showCitationManager}
          onToggleVisibility={() => setShowCitationManager(!showCitationManager)}
        />
      </div>
    );
  }
);

DualAIEditor.displayName = 'DualAIEditor';
