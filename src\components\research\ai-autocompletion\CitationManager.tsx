/**
 * Citation Manager Component
 * Handles automatic citation storage, formatting, and reference management for AI-generated content
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Quote, 
  ExternalLink, 
  Copy, 
  Download, 
  Trash2,
  BookOpen,
  Calendar,
  User,
  Link as LinkIcon,
  FileText,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';
import { CitationReference } from './ai-autocompletion.service';
import { cn } from '@/lib/utils';

interface CitationManagerProps {
  citations: CitationReference[];
  onCitationRemove: (citationId: string) => void;
  onCitationUpdate: (citation: CitationReference) => void;
  citationStyle: 'APA' | 'MLA';
  onStyleChange: (style: 'APA' | 'MLA') => void;
  isVisible: boolean;
  onToggleVisibility: () => void;
  className?: string;
}

export function CitationManager({
  citations,
  onCitationRemove,
  onCitationUpdate,
  citationStyle,
  onStyleChange,
  isVisible,
  onToggleVisibility,
  className = ''
}: CitationManagerProps) {
  const [selectedCitations, setSelectedCitations] = useState<Set<string>>(new Set());
  const [showSettings, setShowSettings] = useState(false);
  const [sortBy, setSortBy] = useState<'date' | 'title' | 'author'>('date');

  // Format citation according to style
  const formatCitation = useCallback((citation: CitationReference): string => {
    const { title, authors, year, url, source } = citation;
    
    if (citationStyle === 'APA') {
      const authorStr = authors && authors.length > 0 
        ? authors.length === 1 
          ? authors[0]
          : authors.length === 2
            ? `${authors[0]} & ${authors[1]}`
            : `${authors[0]} et al.`
        : 'Unknown Author';
      
      const yearStr = year ? `(${year})` : '(n.d.)';
      const titleStr = title;
      const sourceStr = source || url;
      
      return `${authorStr} ${yearStr}. ${titleStr}. Retrieved from ${sourceStr}`;
    } else { // MLA
      const authorStr = authors && authors.length > 0 
        ? authors.length === 1 
          ? authors[0]
          : authors.length === 2
            ? `${authors[0]} and ${authors[1]}`
            : `${authors[0]} et al.`
        : 'Unknown Author';
      
      const titleStr = `"${title}"`;
      const sourceStr = source || new URL(url).hostname;
      const yearStr = year ? `, ${year}` : '';
      
      return `${authorStr}. ${titleStr} ${sourceStr}${yearStr}. Web.`;
    }
  }, [citationStyle]);

  // Generate in-text citation
  const generateInTextCitation = useCallback((citation: CitationReference): string => {
    const { authors, year } = citation;
    
    if (citationStyle === 'APA') {
      const authorStr = authors && authors.length > 0 
        ? authors.length === 1 
          ? authors[0].split(' ').pop() // Last name only
          : authors.length === 2
            ? `${authors[0].split(' ').pop()} & ${authors[1].split(' ').pop()}`
            : `${authors[0].split(' ').pop()} et al.`
        : 'Unknown';
      
      const yearStr = year ? year : 'n.d.';
      return `(${authorStr}, ${yearStr})`;
    } else { // MLA
      const authorStr = authors && authors.length > 0 
        ? authors[0].split(' ').pop() // Last name only
        : 'Unknown';
      
      return `(${authorStr})`;
    }
  }, [citationStyle]);

  // Sort citations
  const sortedCitations = useCallback(() => {
    return [...citations].sort((a, b) => {
      switch (sortBy) {
        case 'title':
          return a.title.localeCompare(b.title);
        case 'author':
          const authorA = a.authors?.[0] || 'Unknown';
          const authorB = b.authors?.[0] || 'Unknown';
          return authorA.localeCompare(authorB);
        case 'date':
        default:
          return new Date(b.year || 0).getTime() - new Date(a.year || 0).getTime();
      }
    });
  }, [citations, sortBy]);

  // Copy citation to clipboard
  const copyCitation = useCallback(async (citation: CitationReference) => {
    const formattedCitation = formatCitation(citation);
    try {
      await navigator.clipboard.writeText(formattedCitation);
      toast.success('Citation copied to clipboard');
    } catch (error) {
      console.error('Failed to copy citation:', error);
      toast.error('Failed to copy citation');
    }
  }, [formatCitation]);

  // Copy in-text citation
  const copyInTextCitation = useCallback(async (citation: CitationReference) => {
    const inTextCitation = generateInTextCitation(citation);
    try {
      await navigator.clipboard.writeText(inTextCitation);
      toast.success('In-text citation copied');
    } catch (error) {
      console.error('Failed to copy in-text citation:', error);
      toast.error('Failed to copy in-text citation');
    }
  }, [generateInTextCitation]);

  // Export all citations
  const exportCitations = useCallback(() => {
    const formattedCitations = sortedCitations()
      .map(citation => formatCitation(citation))
      .join('\n\n');
    
    const blob = new Blob([formattedCitations], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `references-${citationStyle.toLowerCase()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Citations exported successfully');
  }, [sortedCitations, formatCitation, citationStyle]);

  // Toggle citation selection
  const toggleCitationSelection = useCallback((citationId: string) => {
    setSelectedCitations(prev => {
      const newSet = new Set(prev);
      if (newSet.has(citationId)) {
        newSet.delete(citationId);
      } else {
        newSet.add(citationId);
      }
      return newSet;
    });
  }, []);

  // Remove selected citations
  const removeSelectedCitations = useCallback(() => {
    selectedCitations.forEach(citationId => {
      onCitationRemove(citationId);
    });
    setSelectedCitations(new Set());
    toast.success(`Removed ${selectedCitations.size} citation(s)`);
  }, [selectedCitations, onCitationRemove]);

  if (!isVisible) return null;

  return (
    <div className={cn("fixed right-6 top-20 z-40 w-96", className)}>
      <Card className="bg-white shadow-xl border border-gray-200 max-h-[70vh] flex flex-col">
        <CardHeader className="pb-3 border-b">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Quote className="h-5 w-5 text-blue-600" />
              Citations & References
            </CardTitle>
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
                className="h-8 w-8 p-0"
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleVisibility}
                className="h-8 w-8 p-0"
              >
                <LinkIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {/* Stats and Controls */}
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {citations.length} citations
              </Badge>
              <Badge variant="outline" className="text-xs">
                {citationStyle}
              </Badge>
            </div>
            
            {citations.length > 0 && (
              <div className="flex gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={exportCitations}
                  className="h-6 px-2 text-xs"
                >
                  <Download className="h-3 w-3 mr-1" />
                  Export
                </Button>
                {selectedCitations.size > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={removeSelectedCitations}
                    className="h-6 px-2 text-xs text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Remove ({selectedCitations.size})
                  </Button>
                )}
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent className="p-0 flex-1 overflow-hidden">
          {/* Settings Panel */}
          {showSettings && (
            <div className="p-4 border-b bg-gray-50 space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Citation Style</span>
                <div className="flex gap-1">
                  <Button
                    variant={citationStyle === 'APA' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => onStyleChange('APA')}
                    className="h-6 px-3 text-xs"
                  >
                    APA
                  </Button>
                  <Button
                    variant={citationStyle === 'MLA' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => onStyleChange('MLA')}
                    className="h-6 px-3 text-xs"
                  >
                    MLA
                  </Button>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Sort By</span>
                <select 
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'date' | 'title' | 'author')}
                  className="text-xs border rounded px-2 py-1"
                >
                  <option value="date">Date</option>
                  <option value="title">Title</option>
                  <option value="author">Author</option>
                </select>
              </div>
            </div>
          )}

          {/* Citations List */}
          {citations.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <BookOpen className="h-12 w-12 mx-auto mb-3 text-gray-400" />
              <div className="text-sm font-medium mb-1">No citations yet</div>
              <div className="text-xs">Citations will appear here when you accept AI suggestions with research</div>
            </div>
          ) : (
            <ScrollArea className="flex-1">
              <div className="p-4 space-y-3">
                {sortedCitations().map((citation, index) => (
                  <Card 
                    key={citation.id} 
                    className={cn(
                      "p-3 hover:bg-gray-50 transition-colors cursor-pointer border",
                      selectedCitations.has(citation.id) && "bg-blue-50 border-blue-200"
                    )}
                    onClick={() => toggleCitationSelection(citation.id)}
                  >
                    <div className="space-y-2">
                      {/* Citation Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-900 leading-tight">
                            {citation.title}
                          </div>
                          {citation.authors && citation.authors.length > 0 && (
                            <div className="flex items-center gap-1 mt-1">
                              <User className="h-3 w-3 text-gray-400" />
                              <span className="text-xs text-gray-600">
                                {citation.authors.join(', ')}
                              </span>
                            </div>
                          )}
                          {citation.year && (
                            <div className="flex items-center gap-1 mt-1">
                              <Calendar className="h-3 w-3 text-gray-400" />
                              <span className="text-xs text-gray-600">{citation.year}</span>
                            </div>
                          )}
                        </div>
                        
                        <div className="flex gap-1 ml-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              copyInTextCitation(citation);
                            }}
                            className="h-6 w-6 p-0"
                            title="Copy in-text citation"
                          >
                            <Quote className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              copyCitation(citation);
                            }}
                            className="h-6 w-6 p-0"
                            title="Copy full citation"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(citation.url, '_blank');
                            }}
                            className="h-6 w-6 p-0"
                            title="Open source"
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      {/* Formatted Citation Preview */}
                      <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded border-l-2 border-blue-400">
                        {formatCitation(citation)}
                      </div>

                      {/* Source Snippet */}
                      {citation.snippet && (
                        <div className="text-xs text-gray-500 italic">
                          "{citation.snippet}..."
                        </div>
                      )}
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
