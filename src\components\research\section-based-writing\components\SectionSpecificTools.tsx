/**
 * Section-Specific Tools Component
 * Provides granular, section-specific AI tools and functionality
 */

import React, { useState } from 'react';
import { 
  Wand2, 
  Search, 
  RefreshCw, 
  PenTool, 
  Lightbulb,
  Target,
  CheckCircle,
  Quote,
  TrendingUp,
  Layers,
  Sparkles,
  BookOpen,
  FlaskConical,
  BarChart3,
  FileText,
  Hash,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { toast } from 'sonner';

import { sectionContextService } from '../services/section-context.service';
import { ResearchSection } from '../types';

interface SectionSpecificToolsProps {
  section: ResearchSection;
  content: string;
  selectedText?: string;
  onContentChange: (content: string) => void;
  isCompactMode?: boolean;
}

interface SectionTool {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  requiresSearch?: boolean;
  category: 'generate' | 'enhance' | 'review' | 'format';
}

export function SectionSpecificTools({
  section,
  content,
  selectedText,
  onContentChange,
  isCompactMode = false
}: SectionSpecificToolsProps) {
  const [isProcessing, setIsProcessing] = useState<string | null>(null);

  // Section-specific tools configuration
  const getSectionTools = (sectionId: string): SectionTool[] => {
    switch (sectionId) {
      case 'title':
        return [
          { id: 'generate-title', name: 'Generate Title', description: 'AI-generate compelling research titles', icon: Wand2, category: 'generate' },
          { id: 'suggest-titles', name: 'Title Suggestions', description: 'Get multiple title options', icon: Lightbulb, category: 'generate' },
          { id: 'enhance-title', name: 'Enhance Title', description: 'Improve existing title', icon: PenTool, category: 'enhance' },
          { id: 'check-title', name: 'Review Title', description: 'Check title effectiveness', icon: CheckCircle, category: 'review' }
        ];
      
      case 'abstract':
        return [
          { id: 'generate-abstract', name: 'Generate Abstract', description: 'Create comprehensive abstract', icon: Wand2, category: 'generate' },
          { id: 'enhance-abstract', name: 'Enhance Abstract', description: 'Improve clarity and impact', icon: PenTool, category: 'enhance' },
          { id: 'check-length', name: 'Check Length', description: 'Verify word count and structure', icon: Target, category: 'review' },
          { id: 'format-abstract', name: 'Format Abstract', description: 'Apply academic formatting', icon: Layers, category: 'format' }
        ];

      case 'keywords':
        return [
          { id: 'generate-keywords', name: 'Generate Keywords', description: 'Extract relevant keywords', icon: Hash, category: 'generate' },
          { id: 'suggest-keywords', name: 'Keyword Suggestions', description: 'Get keyword recommendations', icon: Lightbulb, category: 'generate' },
          { id: 'optimize-keywords', name: 'Optimize Keywords', description: 'Improve keyword selection', icon: TrendingUp, category: 'enhance' }
        ];

      case 'introduction':
        return [
          { id: 'generate-intro', name: 'Generate Introduction', description: 'Create full introduction with literature review', icon: Wand2, requiresSearch: true, category: 'generate' },
          { id: 'add-literature', name: 'Add Literature Review', description: 'Enhance with recent research', icon: BookOpen, requiresSearch: true, category: 'enhance' },
          { id: 'improve-flow', name: 'Improve Flow', description: 'Enhance logical structure', icon: TrendingUp, category: 'enhance' },
          { id: 'add-citations', name: 'Add Citations', description: 'Include academic references', icon: Quote, requiresSearch: true, category: 'enhance' },
          { id: 'review-intro', name: 'Review Introduction', description: 'Check completeness and quality', icon: CheckCircle, category: 'review' }
        ];

      case 'methodology':
        return [
          { id: 'generate-methodology', name: 'Generate Methodology', description: 'Create detailed methodology section', icon: FlaskConical, requiresSearch: true, category: 'generate' },
          { id: 'suggest-methods', name: 'Method Suggestions', description: 'Recommend research methods', icon: Lightbulb, requiresSearch: true, category: 'generate' },
          { id: 'enhance-methods', name: 'Enhance Methods', description: 'Improve methodological rigor', icon: PenTool, category: 'enhance' },
          { id: 'add-validation', name: 'Add Validation', description: 'Include validation techniques', icon: CheckCircle, requiresSearch: true, category: 'enhance' },
          { id: 'review-methodology', name: 'Review Methodology', description: 'Check methodological soundness', icon: Target, category: 'review' }
        ];

      case 'results':
        return [
          { id: 'generate-results', name: 'Generate Results', description: 'Structure results presentation', icon: BarChart3, category: 'generate' },
          { id: 'improve-clarity', name: 'Improve Clarity', description: 'Enhance result presentation', icon: PenTool, category: 'enhance' },
          { id: 'add-analysis', name: 'Add Analysis', description: 'Include statistical analysis', icon: TrendingUp, category: 'enhance' },
          { id: 'format-results', name: 'Format Results', description: 'Apply proper formatting', icon: Layers, category: 'format' },
          { id: 'review-results', name: 'Review Results', description: 'Check accuracy and completeness', icon: CheckCircle, category: 'review' }
        ];

      case 'discussion':
        return [
          { id: 'generate-discussion', name: 'Generate Discussion', description: 'Create comprehensive discussion', icon: Lightbulb, requiresSearch: true, category: 'generate' },
          { id: 'interpret-results', name: 'Interpret Results', description: 'Add result interpretation', icon: TrendingUp, category: 'enhance' },
          { id: 'add-implications', name: 'Add Implications', description: 'Discuss broader implications', icon: Sparkles, requiresSearch: true, category: 'enhance' },
          { id: 'address-limitations', name: 'Address Limitations', description: 'Include study limitations', icon: Target, category: 'enhance' },
          { id: 'review-discussion', name: 'Review Discussion', description: 'Check argumentation quality', icon: CheckCircle, category: 'review' }
        ];

      case 'conclusion':
        return [
          { id: 'generate-conclusion', name: 'Generate Conclusion', description: 'Create impactful conclusion', icon: Wand2, category: 'generate' },
          { id: 'summarize-findings', name: 'Summarize Findings', description: 'Highlight key findings', icon: Target, category: 'enhance' },
          { id: 'future-work', name: 'Future Work', description: 'Suggest future research', icon: TrendingUp, category: 'enhance' },
          { id: 'review-conclusion', name: 'Review Conclusion', description: 'Check impact and completeness', icon: CheckCircle, category: 'review' }
        ];

      case 'references':
        return [
          { id: 'generate-references', name: 'Generate References', description: 'Create reference list', icon: Quote, category: 'generate' },
          { id: 'format-references', name: 'Format References', description: 'Apply citation style', icon: Layers, category: 'format' },
          { id: 'check-references', name: 'Check References', description: 'Verify reference accuracy', icon: CheckCircle, category: 'review' }
        ];

      default:
        return [];
    }
  };

  const tools = getSectionTools(section.id);

  const handleToolExecution = async (tool: SectionTool) => {
    setIsProcessing(tool.id);

    try {
      // Get section context
      const toolContext = sectionContextService.getSectionToolContext(section.id, selectedText);
      
      // Import AI service
      const { enhancedAIService } = await import('../../paper-generator/services/enhanced-ai.service');
      
      let result: string;

      if (tool.requiresSearch) {
        // Generate content with search
        result = await sectionContextService.generateSectionContent(section.id, {
          includeSearch: true,
          maxSearchResults: 5,
          searchDepth: 'advanced',
          citationStyle: 'APA',
          enhancementType: 'clarity',
          contextFromPreviousSections: true
        });
      } else {
        // Generate content without search
        const prompt = buildToolPrompt(tool, toolContext);
        result = await enhancedAIService.generateText(prompt, undefined, {
          maxTokens: getTokenLimitForTool(tool),
          temperature: 0.7
        });
      }

      // Update content based on tool type
      if (tool.category === 'generate' && !selectedText) {
        // Replace entire content for generation tools
        onContentChange(result);
        sectionContextService.updateSectionContent(section.id, result, true);
      } else if (selectedText) {
        // Replace selected text
        const newContent = content.replace(selectedText, result);
        onContentChange(newContent);
        sectionContextService.updateSectionContent(section.id, newContent, false);
      } else {
        // Append to existing content
        const newContent = content + '\n\n' + result;
        onContentChange(newContent);
        sectionContextService.updateSectionContent(section.id, newContent, false);
      }

      toast.success(`${tool.name} completed successfully`);
    } catch (error: any) {
      console.error('Tool execution error:', error);
      toast.error(error.message || `Failed to execute ${tool.name}`);
    } finally {
      setIsProcessing(null);
    }
  };

  const buildToolPrompt = (tool: SectionTool, context: any): string => {
    const { currentSection, previousSections, paperMetadata } = context;
    
    let prompt = '';
    
    // Add context from previous sections
    if (Object.keys(previousSections).length > 0) {
      prompt += 'Context from previous sections:\n';
      Object.entries(previousSections).forEach(([id, content]) => {
        prompt += `\n${id.toUpperCase()}:\n${(content as string).slice(0, 300)}...\n`;
      });
      prompt += '\n';
    }

    // Add tool-specific prompt
    switch (tool.id) {
      case 'generate-title':
        prompt += `Generate 5 compelling research paper titles for a study about: ${paperMetadata.title || 'the research topic'}. Make them specific, clear, and academically appropriate.`;
        break;
      case 'suggest-titles':
        prompt += `Suggest 8 alternative titles for the research paper: "${paperMetadata.title}". Provide variations in style and focus.`;
        break;
      case 'enhance-title':
        prompt += `Improve this research paper title: "${paperMetadata.title}". Make it more compelling, specific, and academically sound.`;
        break;
      default:
        prompt += `${tool.description} for the ${currentSection.name} section of the research paper "${paperMetadata.title}".`;
    }

    return prompt;
  };

  const getTokenLimitForTool = (tool: SectionTool): number => {
    switch (tool.category) {
      case 'generate':
        return 2048;
      case 'enhance':
        return 1024;
      case 'review':
        return 512;
      case 'format':
        return 512;
      default:
        return 1024;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'generate':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'enhance':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'format':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (tools.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <FileText className="h-8 w-8 mx-auto mb-2 text-gray-400" />
        <p>No specific tools available for this section</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium text-gray-900">
          {section.name} Tools
        </h4>
        <Badge variant="outline" className="text-xs">
          {tools.length} tools available
        </Badge>
      </div>

      <div className={`grid gap-3 ${isCompactMode ? 'grid-cols-2' : 'grid-cols-1'}`}>
        {tools.map(tool => {
          const IconComponent = tool.icon;
          const isProcessingTool = isProcessing === tool.id;
          
          return (
            <TooltipProvider key={tool.id}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    className={`h-auto p-3 justify-start text-left ${isCompactMode ? 'flex-col space-y-1' : 'flex-row space-x-3'}`}
                    onClick={() => handleToolExecution(tool)}
                    disabled={isProcessingTool || !!isProcessing}
                  >
                    <div className={`flex items-center ${isCompactMode ? 'space-x-1' : 'space-x-3'}`}>
                      {isProcessingTool ? (
                        <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                      ) : (
                        <IconComponent className="h-4 w-4 text-gray-600" />
                      )}
                      <span className={`font-medium ${isCompactMode ? 'text-xs' : 'text-sm'}`}>
                        {tool.name}
                      </span>
                      {tool.requiresSearch && (
                        <Search className="h-3 w-3 text-blue-500" />
                      )}
                    </div>
                    
                    {!isCompactMode && (
                      <div className="flex-1 min-w-0">
                        <p className="text-xs text-gray-600 truncate">
                          {tool.description}
                        </p>
                        <Badge 
                          variant="outline" 
                          className={`text-xs mt-1 ${getCategoryColor(tool.category)}`}
                        >
                          {tool.category}
                        </Badge>
                      </div>
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="max-w-xs">
                  <p className="text-xs">{tool.description}</p>
                  {tool.requiresSearch && (
                    <p className="text-xs text-blue-600 mt-1">
                      • Includes academic search
                    </p>
                  )}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}
      </div>
    </div>
  );
}
