/**
 * Section Template Toggle Component
 * Allows users to switch between freeform and template-based writing modes
 */

import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Layout, 
  Settings, 
  BookOpen,
  Target,
  ChevronDown,
  Check
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { toast } from 'sonner';

import { sectionContextService } from '../services/section-context.service';
import { DEFAULT_RESEARCH_TEMPLATE } from '../constants';
import { SectionWritingContext } from '../types';

interface SectionTemplateToggleProps {
  className?: string;
}

export function SectionTemplateToggle({ className = '' }: SectionTemplateToggleProps) {
  const [context, setContext] = useState<SectionWritingContext>(sectionContextService.getContext());
  const [isSetupOpen, setIsSetupOpen] = useState(false);
  const [setupData, setSetupData] = useState({
    title: '',
    researchField: '',
    keywords: '',
    authors: ''
  });

  useEffect(() => {
    const unsubscribe = sectionContextService.subscribe(setContext);
    return unsubscribe;
  }, []);

  const isTemplateMode = context.writingMode === 'template';

  const handleToggleMode = () => {
    if (isTemplateMode) {
      // Exit template mode
      sectionContextService.exitSectionMode();
      toast.success('Switched to freeform writing mode');
    } else {
      // Enter template mode - show setup dialog
      setIsSetupOpen(true);
    }
  };

  const handleSetupComplete = () => {
    const { title, researchField, keywords, authors } = setupData;
    
    if (!title.trim()) {
      toast.error('Please enter a paper title');
      return;
    }

    // Initialize section mode
    sectionContextService.initializeSectionMode(title, researchField);
    
    // Update metadata
    sectionContextService.updatePaperMetadata({
      title: title.trim(),
      researchField: researchField.trim(),
      keywords: keywords.split(',').map(k => k.trim()).filter(k => k),
      authors: authors.split(',').map(a => a.trim()).filter(a => a)
    });

    setIsSetupOpen(false);
    toast.success('Research paper template activated');
  };

  const getProgress = () => {
    const progress = sectionContextService.getProgress();
    return {
      completed: progress.completedSections,
      total: progress.totalSections,
      percentage: Math.round((progress.completedSections / progress.totalSections) * 100)
    };
  };

  const progress = getProgress();

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Mode Toggle */}
      <div className="flex items-center space-x-2">
        <div className="flex items-center space-x-2 text-sm">
          <Layout className="h-4 w-4 text-gray-600" />
          <span className="text-gray-700">Template Mode</span>
        </div>
        <Switch
          checked={isTemplateMode}
          onCheckedChange={handleToggleMode}
          className="data-[state=checked]:bg-blue-600"
        />
      </div>

      {/* Template Status */}
      {isTemplateMode && (
        <>
          <div className="h-4 w-px bg-gray-300" />
          
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-8">
                <FileText className="h-3 w-3 mr-2" />
                <span className="text-xs">
                  {progress.completed}/{progress.total} sections
                </span>
                <Badge variant="secondary" className="ml-2 text-xs">
                  {progress.percentage}%
                </Badge>
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="end">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-gray-900">Paper Progress</h4>
                  <Badge variant="outline" className="text-xs">
                    {context.paperMetadata.title || 'Untitled'}
                  </Badge>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Sections Complete</span>
                    <span className="font-medium">{progress.completed}/{progress.total}</span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress.percentage}%` }}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
                    <div>
                      <div className="font-medium">Word Count</div>
                      <div>{sectionContextService.getProgress().wordCountProgress.current.toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="font-medium">Research Field</div>
                      <div>{context.paperMetadata.researchField || 'Not specified'}</div>
                    </div>
                  </div>
                </div>

                <div className="pt-2 border-t border-gray-200">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full text-xs"
                    onClick={() => setIsSetupOpen(true)}
                  >
                    <Settings className="h-3 w-3 mr-2" />
                    Edit Paper Settings
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </>
      )}

      {/* Setup Dialog */}
      <Dialog open={isSetupOpen} onOpenChange={setIsSetupOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-blue-600" />
              <span>Research Paper Setup</span>
            </DialogTitle>
            <DialogDescription>
              Configure your research paper template to get started with structured writing.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title" className="text-sm font-medium">
                Paper Title *
              </Label>
              <Input
                id="title"
                placeholder="Enter your research paper title"
                value={setupData.title}
                onChange={(e) => setSetupData(prev => ({ ...prev, title: e.target.value }))}
                className="text-sm"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="field" className="text-sm font-medium">
                Research Field
              </Label>
              <Select
                value={setupData.researchField}
                onValueChange={(value) => setSetupData(prev => ({ ...prev, researchField: value }))}
              >
                <SelectTrigger className="text-sm">
                  <SelectValue placeholder="Select research field" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="computer-science">Computer Science</SelectItem>
                  <SelectItem value="psychology">Psychology</SelectItem>
                  <SelectItem value="biology">Biology</SelectItem>
                  <SelectItem value="chemistry">Chemistry</SelectItem>
                  <SelectItem value="physics">Physics</SelectItem>
                  <SelectItem value="mathematics">Mathematics</SelectItem>
                  <SelectItem value="engineering">Engineering</SelectItem>
                  <SelectItem value="medicine">Medicine</SelectItem>
                  <SelectItem value="social-sciences">Social Sciences</SelectItem>
                  <SelectItem value="business">Business</SelectItem>
                  <SelectItem value="education">Education</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="keywords" className="text-sm font-medium">
                Keywords
              </Label>
              <Input
                id="keywords"
                placeholder="keyword1, keyword2, keyword3"
                value={setupData.keywords}
                onChange={(e) => setSetupData(prev => ({ ...prev, keywords: e.target.value }))}
                className="text-sm"
              />
              <p className="text-xs text-gray-600">Separate keywords with commas</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="authors" className="text-sm font-medium">
                Authors
              </Label>
              <Input
                id="authors"
                placeholder="Author 1, Author 2"
                value={setupData.authors}
                onChange={(e) => setSetupData(prev => ({ ...prev, authors: e.target.value }))}
                className="text-sm"
              />
              <p className="text-xs text-gray-600">Separate authors with commas</p>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsSetupOpen(false)}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleSetupComplete}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Check className="h-4 w-4 mr-2" />
              Start Writing
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
