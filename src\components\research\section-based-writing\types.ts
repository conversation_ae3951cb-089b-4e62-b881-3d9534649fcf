/**
 * Types for Section-Based Research Paper Writing
 */

import { LucideIcon } from 'lucide-react';

// Research paper section definition
export interface ResearchSection {
  id: string;
  name: string;
  description: string;
  icon: LucideIcon;
  color: string;
  order: number;
  required: boolean;
  estimatedWords: number;
  aiPromptTemplate: string;
  searchEnabled: boolean; // Whether this section benefits from Tavily search
  contextDependencies: string[]; // Which sections this section should reference
}

// Section content and metadata
export interface SectionContent {
  id: string;
  content: string;
  wordCount: number;
  lastModified: Date;
  status: 'empty' | 'draft' | 'review' | 'complete';
  aiGenerated: boolean;
  citations: string[];
  metadata: {
    aiModel?: string;
    generationPrompt?: string;
    searchResults?: any[];
    userFeedback?: string;
  };
}

// Section-based writing context
export interface SectionWritingContext {
  currentSection: string | null;
  sections: Record<string, SectionContent>;
  paperMetadata: {
    title: string;
    abstract: string;
    keywords: string[];
    researchField: string;
    authors: string[];
  };
  writingMode: 'template' | 'freeform';
  showSectionNavigation: boolean;
}

// AI tool configuration for sections
export interface SectionAITool {
  id: string;
  name: string;
  description: string;
  icon: string;
  applicableSections: string[];
  requiresSearch: boolean;
  promptTemplate: (context: SectionToolContext) => string;
  mode: 'replace' | 'insert' | 'enhance' | 'generate';
}

// Context for section-specific AI tools
export interface SectionToolContext {
  currentSection: ResearchSection;
  sectionContent: string;
  previousSections: Record<string, string>;
  paperMetadata: SectionWritingContext['paperMetadata'];
  selectedText?: string;
  searchResults?: any[];
}

// Section writing progress
export interface SectionProgress {
  totalSections: number;
  completedSections: number;
  currentSection: string | null;
  estimatedTimeRemaining: number;
  wordCountProgress: {
    current: number;
    target: number;
  };
}

// Section navigation state
export interface SectionNavigationState {
  isVisible: boolean;
  expandedSections: string[];
  sectionFilter: 'all' | 'completed' | 'pending' | 'in-progress';
  showProgress: boolean;
}

// AI enhancement options for sections
export interface SectionEnhancementOptions {
  includeSearch: boolean;
  maxSearchResults: number;
  searchDepth: 'basic' | 'advanced' | 'comprehensive';
  citationStyle: 'APA' | 'MLA' | 'Chicago' | 'IEEE';
  enhancementType: 'clarity' | 'academic-tone' | 'expand' | 'citations' | 'structure';
  contextFromPreviousSections: boolean;
}

// Section template configuration
export interface SectionTemplate {
  id: string;
  name: string;
  description: string;
  sections: ResearchSection[];
  defaultMetadata: Partial<SectionWritingContext['paperMetadata']>;
  aiModelRecommendations: Record<string, string>; // section -> model mapping
}

// Events for section-based writing
export interface SectionWritingEvents {
  onSectionChange: (sectionId: string) => void;
  onSectionComplete: (sectionId: string, content: SectionContent) => void;
  onSectionGenerate: (sectionId: string, options: SectionEnhancementOptions) => void;
  onSectionEnhance: (sectionId: string, enhancementType: string) => void;
  onTemplateToggle: (enabled: boolean) => void;
  onProgressUpdate: (progress: SectionProgress) => void;
}
