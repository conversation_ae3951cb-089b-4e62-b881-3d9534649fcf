/**
 * Inline Autocompletion Component
 * Provides real-time sentence completion as ghost text directly in the editor
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Check, 
  X, 
  Loader2,
  Zap,
  ArrowRight
} from 'lucide-react';
import { enhancedAIService } from '../paper-generator/enhanced-ai.service';
import { performanceOptimizer } from './performance-optimizer';
import { cn } from '@/lib/utils';

interface AutocompletionSuggestion {
  id: string;
  completion: string;
  confidence: number;
  reasoning: string;
}

interface InlineAutocompletionProps {
  content: string;
  cursorPosition: number;
  onAcceptCompletion: (completion: string) => void;
  isEnabled: boolean;
  editorElement?: HTMLElement | null;
  className?: string;
}

export function InlineAutocompletion({
  content,
  cursorPosition,
  onAcceptCompletion,
  isEnabled,
  editorElement,
  className = ''
}: InlineAutocompletionProps) {
  const [currentCompletion, setCurrentCompletion] = useState<AutocompletionSuggestion | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showGhostText, setShowGhostText] = useState(false);
  const [ghostTextPosition, setGhostTextPosition] = useState({ x: 0, y: 0 });
  
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const lastContentRef = useRef('');
  const lastCursorRef = useRef(0);
  const rejectionCountRef = useRef(0);
  const lastRejectionTimeRef = useRef(0);

  // Extract current sentence context
  const getCurrentSentenceContext = useCallback((text: string, position: number) => {
    const beforeCursor = text.slice(0, position);
    const afterCursor = text.slice(position);
    
    // Find the start of current sentence
    const sentenceStart = Math.max(
      beforeCursor.lastIndexOf('.'),
      beforeCursor.lastIndexOf('!'),
      beforeCursor.lastIndexOf('?'),
      beforeCursor.lastIndexOf('\n')
    ) + 1;
    
    // Find the end of current sentence
    const sentenceEndMatch = afterCursor.match(/[.!?\n]/);
    const sentenceEnd = sentenceEndMatch ? position + sentenceEndMatch.index! : text.length;
    
    const currentSentence = text.slice(sentenceStart, sentenceEnd).trim();
    const beforeCursorInSentence = text.slice(sentenceStart, position).trim();
    const afterCursorInSentence = text.slice(position, sentenceEnd).trim();
    
    return {
      currentSentence,
      beforeCursorInSentence,
      afterCursorInSentence,
      isInMiddleOfSentence: beforeCursorInSentence.length > 0 && !beforeCursorInSentence.match(/[.!?]$/),
      hasContentAfterCursor: afterCursorInSentence.length > 0
    };
  }, []);

  // Calculate ghost text position
  const calculateGhostTextPosition = useCallback(() => {
    if (!editorElement) return { x: 0, y: 0 };

    try {
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) {
        const rect = editorElement.getBoundingClientRect();
        return { x: rect.left + 20, y: rect.top + 20 };
      }

      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      
      return {
        x: rect.left || 0,
        y: rect.bottom + 5 || 0
      };
    } catch (error) {
      console.warn('Error calculating ghost text position:', error);
      const rect = editorElement.getBoundingClientRect();
      return { x: rect.left + 20, y: rect.top + 20 };
    }
  }, [editorElement]);

  // Generate sentence completion
  const generateCompletion = useCallback(async () => {
    if (!isEnabled || isGenerating) return;

    // Check rejection throttling
    const now = Date.now();
    if (rejectionCountRef.current >= 2 && now - lastRejectionTimeRef.current < 15000) {
      return; // Skip for 15 seconds after 2 rejections
    }

    const sentenceContext = getCurrentSentenceContext(content, cursorPosition);
    
    // Only generate if we're in the middle of a sentence and there's meaningful content
    if (!sentenceContext.isInMiddleOfSentence || 
        sentenceContext.beforeCursorInSentence.length < 10 ||
        sentenceContext.hasContentAfterCursor) {
      setCurrentCompletion(null);
      setShowGhostText(false);
      return;
    }

    setIsGenerating(true);

    try {
      // Create cache key for this specific context
      const contextKey = `${sentenceContext.beforeCursorInSentence.slice(-50)}-completion`;
      const cachedCompletion = performanceOptimizer.getCachedSuggestions(contextKey);
      
      if (cachedCompletion && cachedCompletion.length > 0) {
        const completion: AutocompletionSuggestion = {
          id: `completion-${Date.now()}`,
          completion: cachedCompletion[0].content,
          confidence: cachedCompletion[0].confidence,
          reasoning: 'Cached sentence completion'
        };
        setCurrentCompletion(completion);
        setGhostTextPosition(calculateGhostTextPosition());
        setShowGhostText(true);
        setIsGenerating(false);
        return;
      }

      // Generate new completion
      const prompt = `Complete this sentence naturally and concisely:

"${sentenceContext.beforeCursorInSentence}"

Rules:
1. Complete the sentence with 3-8 words maximum
2. Make it flow naturally from the existing text
3. Don't repeat words already used
4. End with appropriate punctuation if completing the sentence
5. Match the tone and style of the existing text

Return only the completion text, no explanations.`;

      const result = await enhancedAIService.generateText(prompt, 'google/gemini-2.5-flash', {
        maxTokens: 50,
        temperature: 0.7
      });

      const cleanCompletion = result.trim();
      
      if (cleanCompletion && cleanCompletion.length > 0 && cleanCompletion.length < 100) {
        const completion: AutocompletionSuggestion = {
          id: `completion-${Date.now()}`,
          completion: cleanCompletion,
          confidence: 0.8,
          reasoning: 'AI-generated sentence completion'
        };

        // Cache the completion
        performanceOptimizer.cacheSuggestions(contextKey, [{
          id: completion.id,
          content: completion.completion,
          type: 'sentence',
          confidence: completion.confidence,
          hasResearch: false,
          reasoning: completion.reasoning,
          wordCount: completion.completion.split(/\s+/).length,
          timestamp: new Date()
        }]);

        setCurrentCompletion(completion);
        setGhostTextPosition(calculateGhostTextPosition());
        setShowGhostText(true);
        
        // Reset rejection count on successful generation
        rejectionCountRef.current = 0;
      } else {
        setCurrentCompletion(null);
        setShowGhostText(false);
      }
    } catch (error) {
      console.error('Error generating completion:', error);
      setCurrentCompletion(null);
      setShowGhostText(false);
    } finally {
      setIsGenerating(false);
    }
  }, [content, cursorPosition, isEnabled, isGenerating, getCurrentSentenceContext, calculateGhostTextPosition]);

  // Debounced completion generation
  useEffect(() => {
    if (!isEnabled) {
      setCurrentCompletion(null);
      setShowGhostText(false);
      return;
    }

    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Check if content or cursor changed significantly
    const contentChanged = content !== lastContentRef.current;
    const cursorMoved = Math.abs(cursorPosition - lastCursorRef.current) > 1;
    
    if (contentChanged || cursorMoved) {
      // Hide current completion immediately when typing
      setShowGhostText(false);
      
      // Set new timeout for generation
      debounceTimeoutRef.current = setTimeout(() => {
        generateCompletion();
      }, 800); // 800ms delay for responsiveness
    }

    lastContentRef.current = content;
    lastCursorRef.current = cursorPosition;

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [content, cursorPosition, isEnabled, generateCompletion]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!showGhostText || !currentCompletion) return;

      if (event.key === 'Tab') {
        event.preventDefault();
        handleAcceptCompletion();
      } else if (event.key === 'Escape') {
        event.preventDefault();
        handleRejectCompletion();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showGhostText, currentCompletion]);

  // Accept completion
  const handleAcceptCompletion = useCallback(() => {
    if (currentCompletion) {
      onAcceptCompletion(currentCompletion.completion);
      setCurrentCompletion(null);
      setShowGhostText(false);
      rejectionCountRef.current = 0;
    }
  }, [currentCompletion, onAcceptCompletion]);

  // Reject completion
  const handleRejectCompletion = useCallback(() => {
    setCurrentCompletion(null);
    setShowGhostText(false);
    rejectionCountRef.current += 1;
    lastRejectionTimeRef.current = Date.now();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  if (!isEnabled || (!showGhostText && !isGenerating)) {
    return null;
  }

  return (
    <>
      {/* Loading indicator */}
      {isGenerating && (
        <div 
          className="fixed z-50 pointer-events-none"
          style={{
            left: `${ghostTextPosition.x}px`,
            top: `${ghostTextPosition.y}px`
          }}
        >
          <div className="flex items-center gap-1 bg-blue-50 text-blue-600 px-2 py-1 rounded-md shadow-sm border border-blue-200">
            <Loader2 className="h-3 w-3 animate-spin" />
            <span className="text-xs">Completing...</span>
          </div>
        </div>
      )}

      {/* Ghost text completion */}
      {showGhostText && currentCompletion && (
        <div 
          className={cn(
            "fixed z-50 pointer-events-none transition-all duration-200",
            className
          )}
          style={{
            left: `${ghostTextPosition.x}px`,
            top: `${ghostTextPosition.y}px`
          }}
        >
          <div className="flex items-center gap-2 bg-white/95 backdrop-blur-sm border border-gray-200 rounded-lg shadow-lg p-2 max-w-sm">
            {/* Ghost text */}
            <div className="flex items-center gap-1">
              <span className="text-gray-400 italic text-sm">
                {currentCompletion.completion}
              </span>
              <ArrowRight className="h-3 w-3 text-gray-400" />
            </div>
            
            {/* Action buttons */}
            <div className="flex items-center gap-1 pointer-events-auto">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAcceptCompletion}
                className="h-6 w-6 p-0 hover:bg-green-100 hover:text-green-700"
                title="Accept (Tab)"
              >
                <Check className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRejectCompletion}
                className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-700"
                title="Reject (Esc)"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            
            {/* Confidence indicator */}
            <Badge variant="outline" className="text-xs pointer-events-none">
              {Math.round(currentCompletion.confidence * 100)}%
            </Badge>
          </div>
          
          {/* Keyboard hint */}
          <div className="text-xs text-gray-500 mt-1 text-center">
            Tab to accept • Esc to reject
          </div>
        </div>
      )}
    </>
  );
}
