/**
 * Performance Optimizer for AI Autocompletion
 * Handles caching, debouncing, and performance optimizations
 */

import { AutocompletionSuggestion, WritingContext } from './ai-autocompletion.service';

export interface PerformanceMetrics {
  averageResponseTime: number;
  cacheHitRate: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  lastRequestTime: number;
}

export interface CacheEntry {
  key: string;
  suggestions: AutocompletionSuggestion[];
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
}

export class PerformanceOptimizer {
  private static instance: PerformanceOptimizer;
  private cache = new Map<string, CacheEntry>();
  private metrics: PerformanceMetrics = {
    averageResponseTime: 0,
    cacheHitRate: 0,
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    lastRequestTime: 0
  };
  
  private readonly maxCacheSize = 100;
  private readonly cacheExpiryTime = 5 * 60 * 1000; // 5 minutes
  private readonly debounceMap = new Map<string, NodeJS.Timeout>();
  private readonly requestQueue: Array<() => Promise<any>> = [];
  private isProcessingQueue = false;

  static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer();
    }
    return PerformanceOptimizer.instance;
  }

  /**
   * Generate cache key from context and options
   */
  generateCacheKey(
    content: string, 
    cursorPosition: number, 
    context: WritingContext,
    options: any
  ): string {
    const contentHash = this.hashString(content.slice(Math.max(0, cursorPosition - 500), cursorPosition + 100));
    const contextHash = this.hashString(JSON.stringify({
      documentType: context.documentType,
      currentSection: context.currentSection,
      writingFlow: context.writingFlow,
      topicKeywords: context.topicKeywords.slice(0, 3)
    }));
    const optionsHash = this.hashString(JSON.stringify(options));
    
    return `${contentHash}-${contextHash}-${optionsHash}`;
  }

  /**
   * Get cached suggestions if available and not expired
   */
  getCachedSuggestions(cacheKey: string): AutocompletionSuggestion[] | null {
    const entry = this.cache.get(cacheKey);
    
    if (!entry) {
      return null;
    }

    // Check if cache entry is expired
    if (Date.now() - entry.timestamp > this.cacheExpiryTime) {
      this.cache.delete(cacheKey);
      return null;
    }

    // Update access metrics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    this.updateCacheHitRate(true);
    return entry.suggestions;
  }

  /**
   * Cache suggestions with LRU eviction
   */
  cacheSuggestions(cacheKey: string, suggestions: AutocompletionSuggestion[]): void {
    // Remove expired entries first
    this.cleanupExpiredEntries();

    // If cache is full, remove least recently used entry
    if (this.cache.size >= this.maxCacheSize) {
      this.evictLRUEntry();
    }

    const entry: CacheEntry = {
      key: cacheKey,
      suggestions,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now()
    };

    this.cache.set(cacheKey, entry);
    this.updateCacheHitRate(false);
  }

  /**
   * Debounced function execution
   */
  debounce<T extends (...args: any[]) => Promise<any>>(
    key: string,
    func: T,
    delay: number
  ): (...args: Parameters<T>) => Promise<ReturnType<T>> {
    return (...args: Parameters<T>): Promise<ReturnType<T>> => {
      return new Promise((resolve, reject) => {
        // Clear existing timeout
        const existingTimeout = this.debounceMap.get(key);
        if (existingTimeout) {
          clearTimeout(existingTimeout);
        }

        // Set new timeout
        const timeout = setTimeout(async () => {
          try {
            const result = await func(...args);
            resolve(result);
          } catch (error) {
            reject(error);
          } finally {
            this.debounceMap.delete(key);
          }
        }, delay);

        this.debounceMap.set(key, timeout);
      });
    };
  }

  /**
   * Queue requests to prevent overwhelming the API
   */
  async queueRequest<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push(async () => {
        try {
          const startTime = Date.now();
          const result = await requestFn();
          const endTime = Date.now();
          
          this.updateMetrics(endTime - startTime, true);
          resolve(result);
        } catch (error) {
          this.updateMetrics(0, false);
          reject(error);
        }
      });

      this.processQueue();
    });
  }

  /**
   * Process request queue with rate limiting
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift();
      if (request) {
        try {
          await request();
        } catch (error) {
          console.error('Request failed:', error);
        }
        
        // Rate limiting: wait 100ms between requests
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    this.isProcessingQueue = false;
  }

  /**
   * Update performance metrics
   */
  private updateMetrics(responseTime: number, success: boolean): void {
    this.metrics.totalRequests++;
    this.metrics.lastRequestTime = Date.now();

    if (success) {
      this.metrics.successfulRequests++;
      
      // Update average response time
      const totalResponseTime = this.metrics.averageResponseTime * (this.metrics.successfulRequests - 1) + responseTime;
      this.metrics.averageResponseTime = totalResponseTime / this.metrics.successfulRequests;
    } else {
      this.metrics.failedRequests++;
    }
  }

  /**
   * Update cache hit rate
   */
  private updateCacheHitRate(isHit: boolean): void {
    const totalCacheRequests = this.metrics.totalRequests;
    if (totalCacheRequests === 0) return;

    // This is a simplified calculation - in a real implementation,
    // you'd want to track cache requests separately
    if (isHit) {
      this.metrics.cacheHitRate = Math.min(1, this.metrics.cacheHitRate + 0.01);
    } else {
      this.metrics.cacheHitRate = Math.max(0, this.metrics.cacheHitRate - 0.005);
    }
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupExpiredEntries(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.cacheExpiryTime) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Evict least recently used cache entry
   */
  private evictLRUEntry(): void {
    let lruKey: string | null = null;
    let lruTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < lruTime) {
        lruTime = entry.lastAccessed;
        lruKey = key;
      }
    }

    if (lruKey) {
      this.cache.delete(lruKey);
    }
  }

  /**
   * Simple string hashing function
   */
  private hashString(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset performance metrics
   */
  resetMetrics(): void {
    this.metrics = {
      averageResponseTime: 0,
      cacheHitRate: 0,
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      lastRequestTime: 0
    };
  }

  /**
   * Clear all caches
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    oldestEntry: number;
    newestEntry: number;
  } {
    let oldestTime = Date.now();
    let newestTime = 0;

    for (const entry of this.cache.values()) {
      if (entry.timestamp < oldestTime) oldestTime = entry.timestamp;
      if (entry.timestamp > newestTime) newestTime = entry.timestamp;
    }

    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hitRate: this.metrics.cacheHitRate,
      oldestEntry: oldestTime,
      newestEntry: newestTime
    };
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    // Clear all debounce timeouts
    for (const timeout of this.debounceMap.values()) {
      clearTimeout(timeout);
    }
    this.debounceMap.clear();

    // Clear cache
    this.cache.clear();

    // Clear request queue
    this.requestQueue.length = 0;
    this.isProcessingQueue = false;
  }
}

export const performanceOptimizer = PerformanceOptimizer.getInstance();
