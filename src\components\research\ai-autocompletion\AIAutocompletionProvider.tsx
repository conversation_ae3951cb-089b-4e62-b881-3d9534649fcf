/**
 * AI Autocompletion Provider
 * Main component that orchestrates real-time AI suggestions and integrates with the editor
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  Wand2, 
  Settings, 
  X, 
  Zap,
  Brain,
  Clock,
  BookOpen
} from 'lucide-react';
import { toast } from 'sonner';
import { 
  aiAutocompletionService, 
  AutocompletionSuggestion, 
  AutocompletionOptions,
  CitationReference 
} from './ai-autocompletion.service';
import { InlineSuggestionDisplay } from './InlineSuggestionDisplay';
import { cn } from '@/lib/utils';

interface AIAutocompletionProviderProps {
  content: string;
  cursorPosition: number;
  onContentInsert: (content: string, position: number) => void;
  onCitationAdd: (citation: CitationReference) => void;
  isEnabled: boolean;
  onToggleEnabled: (enabled: boolean) => void;
  className?: string;
  editorElement?: HTMLElement | null;
}

export function AIAutocompletionProvider({
  content,
  cursorPosition,
  onContentInsert,
  onCitationAdd,
  isEnabled,
  onToggleEnabled,
  className = '',
  editorElement
}: AIAutocompletionProviderProps) {
  // Core state
  const [suggestions, setSuggestions] = useState<AutocompletionSuggestion[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [suggestionPosition, setSuggestionPosition] = useState({ x: 0, y: 0 });
  const [showSuggestions, setShowSuggestions] = useState(false);
  
  // Settings state
  const [options, setOptions] = useState<AutocompletionOptions>({
    maxSuggestions: 2,
    enableResearch: true,
    citationStyle: 'APA',
    responseTime: 'balanced',
    contextWindow: 1000
  });

  // Refs
  const lastContentRef = useRef('');
  const lastCursorRef = useRef(0);
  const generationTimeoutRef = useRef<NodeJS.Timeout>();
  const rejectionCountRef = useRef(0);
  const lastRejectionTimeRef = useRef(0);

  // Calculate cursor position for suggestion display
  const calculateSuggestionPosition = useCallback(() => {
    if (!editorElement) return { x: 0, y: 0 };

    try {
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) {
        // Fallback to editor element position
        const rect = editorElement.getBoundingClientRect();
        return {
          x: rect.left + 20,
          y: rect.top + 20
        };
      }

      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      
      return {
        x: rect.left || 0,
        y: rect.top || 0
      };
    } catch (error) {
      console.warn('Error calculating cursor position:', error);
      const rect = editorElement.getBoundingClientRect();
      return {
        x: rect.left + 20,
        y: rect.top + 20
      };
    }
  }, [editorElement]);

  // Generate suggestions with intelligent triggering
  const generateSuggestions = useCallback(async () => {
    if (!isEnabled || isGenerating) return;

    // Check if we should skip generation due to recent rejections
    const now = Date.now();
    if (rejectionCountRef.current >= 3 && now - lastRejectionTimeRef.current < 30000) {
      return; // Skip for 30 seconds after 3 rejections
    }

    // Clear existing timeout
    if (generationTimeoutRef.current) {
      clearTimeout(generationTimeoutRef.current);
    }

    // Check if content has changed significantly
    const contentDiff = Math.abs(content.length - lastContentRef.current.length);
    const cursorDiff = Math.abs(cursorPosition - lastCursorRef.current);
    
    if (contentDiff < 5 && cursorDiff < 5) {
      return; // Not enough change to warrant new suggestions
    }

    setIsGenerating(true);
    
    try {
      const newSuggestions = await aiAutocompletionService.generateSuggestions(
        content,
        cursorPosition,
        options
      );

      if (newSuggestions.length > 0) {
        setSuggestions(newSuggestions);
        setSuggestionPosition(calculateSuggestionPosition());
        setShowSuggestions(true);
        
        // Reset rejection count on successful generation
        rejectionCountRef.current = 0;
      } else {
        setShowSuggestions(false);
      }

      lastContentRef.current = content;
      lastCursorRef.current = cursorPosition;
    } catch (error) {
      console.error('Error generating suggestions:', error);
      setShowSuggestions(false);
    } finally {
      setIsGenerating(false);
    }
  }, [content, cursorPosition, isEnabled, isGenerating, options, calculateSuggestionPosition]);

  // Trigger suggestion generation with debouncing
  useEffect(() => {
    if (!isEnabled) {
      setShowSuggestions(false);
      return;
    }

    // Clear existing timeout
    if (generationTimeoutRef.current) {
      clearTimeout(generationTimeoutRef.current);
    }

    // Set new timeout based on response time setting
    const delay = options.responseTime === 'fast' ? 800 : 
                  options.responseTime === 'balanced' ? 1500 : 2000;

    generationTimeoutRef.current = setTimeout(() => {
      generateSuggestions();
    }, delay);

    return () => {
      if (generationTimeoutRef.current) {
        clearTimeout(generationTimeoutRef.current);
      }
    };
  }, [content, cursorPosition, generateSuggestions, isEnabled, options.responseTime]);

  // Handle suggestion acceptance
  const handleAcceptSuggestion = useCallback((suggestion: AutocompletionSuggestion) => {
    // Insert content at cursor position
    onContentInsert(suggestion.content, cursorPosition);
    
    // Add citations if present
    if (suggestion.citations) {
      suggestion.citations.forEach(citation => {
        onCitationAdd(citation);
      });
      
      toast.success(`Content added with ${suggestion.citations.length} citation(s)`);
    } else {
      toast.success('Content added to document');
    }

    // Hide suggestions
    setShowSuggestions(false);
    setSuggestions([]);
    
    // Reset rejection count
    rejectionCountRef.current = 0;
  }, [cursorPosition, onContentInsert, onCitationAdd]);

  // Handle suggestion rejection
  const handleRejectSuggestion = useCallback((suggestionId: string) => {
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
    
    // Track rejections
    rejectionCountRef.current += 1;
    lastRejectionTimeRef.current = Date.now();
    
    // Hide if no more suggestions
    if (suggestions.length <= 1) {
      setShowSuggestions(false);
    }
    
    // Show feedback for multiple rejections
    if (rejectionCountRef.current >= 3) {
      toast.info('AI suggestions paused for a moment. Continue writing to re-enable.');
    }
  }, [suggestions.length]);

  // Handle regenerate suggestions
  const handleRegenerateSuggestions = useCallback(() => {
    setSuggestions([]);
    setShowSuggestions(false);
    
    // Force regeneration
    setTimeout(() => {
      generateSuggestions();
    }, 100);
  }, [generateSuggestions]);

  // Update settings
  const updateOptions = useCallback((newOptions: Partial<AutocompletionOptions>) => {
    setOptions(prev => ({ ...prev, ...newOptions }));
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (generationTimeoutRef.current) {
        clearTimeout(generationTimeoutRef.current);
      }
    };
  }, []);

  if (!isEnabled) {
    return (
      <div className={cn("fixed bottom-20 right-6 z-40", className)}>
        <Card className="p-3 bg-gray-50 border-gray-200">
          <div className="flex items-center gap-2">
            <Wand2 className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">AI Autocompletion disabled</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleEnabled(true)}
              className="h-6 px-2 text-xs"
            >
              Enable
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <>
      {/* Settings Panel */}
      {showSettings && (
        <div className={cn("fixed bottom-32 right-6 z-40", className)}>
          <Card className="w-80 bg-white shadow-xl border border-gray-200">
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">AI Autocompletion Settings</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSettings(false)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
            
            <div className="p-4 space-y-4">
              {/* Max Suggestions */}
              <div className="flex items-center justify-between">
                <span className="text-sm">Max Suggestions</span>
                <select 
                  value={options.maxSuggestions}
                  onChange={(e) => updateOptions({ maxSuggestions: parseInt(e.target.value) })}
                  className="text-xs border rounded px-2 py-1"
                >
                  <option value={1}>1</option>
                  <option value={2}>2</option>
                  <option value={3}>3</option>
                </select>
              </div>

              {/* Enable Research */}
              <div className="flex items-center justify-between">
                <span className="text-sm">Research Integration</span>
                <Switch
                  checked={options.enableResearch}
                  onCheckedChange={(checked) => updateOptions({ enableResearch: checked })}
                />
              </div>

              {/* Citation Style */}
              <div className="flex items-center justify-between">
                <span className="text-sm">Citation Style</span>
                <select 
                  value={options.citationStyle}
                  onChange={(e) => updateOptions({ citationStyle: e.target.value as 'APA' | 'MLA' })}
                  className="text-xs border rounded px-2 py-1"
                >
                  <option value="APA">APA</option>
                  <option value="MLA">MLA</option>
                </select>
              </div>

              {/* Response Time */}
              <div className="flex items-center justify-between">
                <span className="text-sm">Response Speed</span>
                <select 
                  value={options.responseTime}
                  onChange={(e) => updateOptions({ responseTime: e.target.value as 'fast' | 'balanced' | 'thorough' })}
                  className="text-xs border rounded px-2 py-1"
                >
                  <option value="fast">Fast</option>
                  <option value="balanced">Balanced</option>
                  <option value="thorough">Thorough</option>
                </select>
              </div>

              {/* Context Window */}
              <div className="flex items-center justify-between">
                <span className="text-sm">Context Window</span>
                <select 
                  value={options.contextWindow}
                  onChange={(e) => updateOptions({ contextWindow: parseInt(e.target.value) })}
                  className="text-xs border rounded px-2 py-1"
                >
                  <option value={500}>500 chars</option>
                  <option value={1000}>1000 chars</option>
                  <option value={1500}>1500 chars</option>
                </select>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Status Indicator */}
      <div className={cn("fixed bottom-20 right-6 z-40", className)}>
        <Card className="p-3 bg-white border border-gray-200 shadow-lg">
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <Wand2 className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">AI Autocompletion</span>
              <Badge 
                variant="outline" 
                className={cn(
                  "text-xs",
                  isGenerating ? "bg-yellow-100 text-yellow-700" : "bg-green-100 text-green-700"
                )}
              >
                {isGenerating ? 'Generating...' : 'Active'}
              </Badge>
            </div>
            
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
                className="h-6 w-6 p-0"
              >
                <Settings className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onToggleEnabled(false)}
                className="h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
          
          {/* Quick Stats */}
          <div className="flex items-center gap-3 mt-2 text-xs text-gray-500">
            <div className="flex items-center gap-1">
              <Brain className="h-3 w-3" />
              <span>{suggestions.length} suggestions</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>{options.responseTime}</span>
            </div>
            {options.enableResearch && (
              <div className="flex items-center gap-1">
                <BookOpen className="h-3 w-3" />
                <span>Research</span>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Inline Suggestion Display */}
      <InlineSuggestionDisplay
        suggestions={suggestions}
        isGenerating={isGenerating}
        onAcceptSuggestion={handleAcceptSuggestion}
        onRejectSuggestion={handleRejectSuggestion}
        onRegenerateSuggestions={handleRegenerateSuggestions}
        position={suggestionPosition}
        isVisible={showSuggestions}
      />
    </>
  );
}
