import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Sparkles, 
  Brain, 
  Search, 
  Check, 
  X, 
  RefreshCw,
  Lightbulb,
  FileText,
  Zap,
  Settings,
  ChevronDown,
  ChevronUp,
  Quote,
  BookOpen,
  Loader2,
  PenTool,
  Target,
  Wand2
} from 'lucide-react';
import { toast } from 'sonner';
import { enhancedAIService } from './paper-generator/enhanced-ai.service';
import { tavilySearchService } from './research-search/services/tavily-search.service';

interface WritingSuggestion {
  id: string;
  content: string;
  type: 'paragraph' | 'section' | 'transition' | 'conclusion';
  confidence: number;
  hasResearch: boolean;
  citations?: string[];
  reasoning: string;
  wordCount: number;
}

interface WritingContext {
  documentType: 'academic' | 'creative' | 'technical' | 'casual';
  currentSection: 'introduction' | 'body' | 'conclusion' | 'unknown';
  tone: 'formal' | 'informal' | 'technical' | 'conversational';
  needsResearch: boolean;
  topicKeywords: string[];
}

interface IntelligentWritingAssistantProps {
  content: string;
  cursorPosition: number;
  onContentInsert: (content: string, position: number) => void;
  isEnabled: boolean;
  onToggleEnabled: (enabled: boolean) => void;
  className?: string;
}

export function IntelligentWritingAssistant({
  content,
  cursorPosition,
  onContentInsert,
  isEnabled,
  onToggleEnabled,
  className = ''
}: IntelligentWritingAssistantProps) {
  // Core state
  const [isVisible, setIsVisible] = useState(false);
  const [writingIntent, setWritingIntent] = useState('');
  const [showIntentDialog, setShowIntentDialog] = useState(false);
  const [hasShownIntentDialog, setHasShownIntentDialog] = useState(false);
  const [suggestions, setSuggestions] = useState<WritingSuggestion[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [writingContext, setWritingContext] = useState<WritingContext | null>(null);
  
  // Settings
  const [autoResearch, setAutoResearch] = useState(true);
  const [writingStyle, setWritingStyle] = useState<'academic' | 'creative' | 'technical' | 'casual'>('academic');
  const [showSettings, setShowSettings] = useState(false);
  const [suggestionsExpanded, setSuggestionsExpanded] = useState(true);
  
  // Refs
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const lastAnalyzedContent = useRef('');

  // Analyze document context
  const analyzeWritingContext = useCallback((documentContent: string): WritingContext => {
    const text = documentContent.toLowerCase();
    const wordCount = documentContent.split(/\s+/).length;
    
    // Detect document type
    let documentType: WritingContext['documentType'] = 'casual';
    if (text.includes('abstract') || text.includes('methodology') || text.includes('references')) {
      documentType = 'academic';
    } else if (text.includes('function') || text.includes('algorithm') || text.includes('implementation')) {
      documentType = 'technical';
    } else if (text.includes('once upon') || text.includes('character') || text.includes('story')) {
      documentType = 'creative';
    }

    // Detect current section
    let currentSection: WritingContext['currentSection'] = 'unknown';
    if (wordCount < 100) {
      currentSection = 'introduction';
    } else if (text.includes('conclusion') || text.includes('in summary') || text.includes('to conclude')) {
      currentSection = 'conclusion';
    } else {
      currentSection = 'body';
    }

    // Determine if research is needed
    const needsResearch = documentType === 'academic' || 
                         text.includes('research') || 
                         text.includes('study') || 
                         text.includes('according to') ||
                         text.includes('evidence');

    // Extract topic keywords
    const topicKeywords = extractKeywords(documentContent);

    return {
      documentType,
      currentSection,
      tone: documentType === 'academic' ? 'formal' : 'conversational',
      needsResearch,
      topicKeywords
    };
  }, []);

  // Extract keywords from content
  const extractKeywords = (text: string): string[] => {
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 4)
      .filter(word => !['that', 'this', 'with', 'from', 'they', 'have', 'been', 'were', 'will', 'would', 'could', 'should'].includes(word));
    
    // Get unique words and return top 5
    const uniqueWords = [...new Set(words)];
    return uniqueWords.slice(0, 5);
  };

  // Trigger writing assistant
  const triggerWritingAssistant = useCallback(() => {
    if (!isEnabled) return;

    // Clear previous timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Debounce to avoid excessive triggers
    debounceTimeoutRef.current = setTimeout(() => {
      const context = analyzeWritingContext(content);
      setWritingContext(context);

      // Show intent dialog only once if this is a new document and user hasn't specified intent
      if (content.trim().length < 50 && !writingIntent && !hasShownIntentDialog) {
        setShowIntentDialog(true);
        setHasShownIntentDialog(true);
        setIsVisible(true);
      } else if (content !== lastAnalyzedContent.current && writingIntent) {
        // Only auto-generate suggestions if user has set an intent
        generateWritingSuggestions(context);
        setIsVisible(true);
      }
      
      lastAnalyzedContent.current = content;
    }, 1500); // 1.5 second debounce
  }, [content, isEnabled, writingIntent, analyzeWritingContext]);

  // Generate writing suggestions
  const generateWritingSuggestions = async (context: WritingContext, userIntent?: string) => {
    setIsGenerating(true);
    try {
      const suggestions: WritingSuggestion[] = [];
      
      // Get context around cursor
      const beforeCursor = content.slice(0, cursorPosition);
      const afterCursor = content.slice(cursorPosition);
      const recentContent = beforeCursor.slice(-500); // Last 500 chars for context

      // Generate different types of suggestions
      const suggestionPromises = [
        generateContinuationSuggestion(recentContent, context, userIntent),
        generateTransitionSuggestion(recentContent, afterCursor, context),
      ];

      // Add research-enhanced suggestion if needed
      if (context.needsResearch && autoResearch && tavilySearchService.isConfigured()) {
        suggestionPromises.push(generateResearchSuggestion(recentContent, context, userIntent));
      }

      const results = await Promise.allSettled(suggestionPromises);
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          suggestions.push(result.value);
        }
      });

      setSuggestions(suggestions);
    } catch (error) {
      console.error('Error generating writing suggestions:', error);
      toast.error('Failed to generate writing suggestions');
    } finally {
      setIsGenerating(false);
    }
  };

  // Generate continuation suggestion
  const generateContinuationSuggestion = async (
    recentContent: string, 
    context: WritingContext, 
    userIntent?: string
  ): Promise<WritingSuggestion | null> => {
    const intentContext = userIntent ? `User wants to write about: ${userIntent}\n\n` : '';
    const prompt = `${intentContext}Continue this ${context.documentType} writing naturally and coherently:

Recent content: "${recentContent}"

Writing context:
- Document type: ${context.documentType}
- Current section: ${context.currentSection}
- Tone: ${context.tone}
- Topic keywords: ${context.topicKeywords.join(', ')}

Generate a well-structured paragraph (100-200 words) that:
1. Flows naturally from the existing content
2. Maintains the established tone and style
3. Advances the main argument or narrative
4. Uses appropriate academic/professional language
5. Provides substantive content, not just filler

Return only the paragraph content, no explanations.`;

    try {
      const result = await enhancedAIService.generateText(prompt, 'google/gemini-2.5-flash', {
        maxTokens: 300,
        temperature: 0.7
      });

      return {
        id: `continuation-${Date.now()}`,
        content: result.trim(),
        type: 'paragraph',
        confidence: 0.85,
        hasResearch: false,
        reasoning: 'Natural continuation based on document context and flow',
        wordCount: result.trim().split(/\s+/).length
      };
    } catch (error) {
      console.error('Error generating continuation suggestion:', error);
      return null;
    }
  };

  // Generate transition suggestion
  const generateTransitionSuggestion = async (
    beforeContent: string,
    afterContent: string,
    context: WritingContext
  ): Promise<WritingSuggestion | null> => {
    if (!afterContent.trim()) return null; // No need for transition if nothing follows

    const prompt = `Create a smooth transition paragraph between these two sections:

Before: "${beforeContent.slice(-200)}"
After: "${afterContent.slice(0, 200)}"

Context: ${context.documentType} writing, ${context.tone} tone

Generate a transitional paragraph (50-100 words) that:
1. Smoothly connects the ideas
2. Maintains logical flow
3. Uses appropriate transition words
4. Matches the document's tone

Return only the transition paragraph.`;

    try {
      const result = await enhancedAIService.generateText(prompt, 'google/gemini-2.5-flash', {
        maxTokens: 150,
        temperature: 0.6
      });

      return {
        id: `transition-${Date.now()}`,
        content: result.trim(),
        type: 'transition',
        confidence: 0.75,
        hasResearch: false,
        reasoning: 'Smooth transition between existing content sections',
        wordCount: result.trim().split(/\s+/).length
      };
    } catch (error) {
      console.error('Error generating transition suggestion:', error);
      return null;
    }
  };

  // Generate research-enhanced suggestion
  const generateResearchSuggestion = async (
    recentContent: string,
    context: WritingContext,
    userIntent?: string
  ): Promise<WritingSuggestion | null> => {
    try {
      // Create search query from context and keywords
      const searchQuery = userIntent || context.topicKeywords.slice(0, 3).join(' ');
      
      if (!searchQuery.trim()) return null;

      // Perform academic search
      const searchResults = await tavilySearchService.searchAcademic(searchQuery, {
        maxResults: 3,
        searchDepth: 'basic',
        includeAnswer: true
      });

      if (!searchResults.results.length) return null;

      // Generate content with research integration
      const researchContext = searchResults.results
        .slice(0, 2)
        .map(result => `Source: ${result.title}\nContent: ${result.content.slice(0, 300)}`)
        .join('\n\n');

      const prompt = `Write a research-informed paragraph using these academic sources:

${researchContext}

Recent document content: "${recentContent}"
Topic focus: ${searchQuery}

Create a paragraph (150-250 words) that:
1. Integrates insights from the research sources
2. Maintains academic tone and rigor
3. Flows naturally from existing content
4. Includes proper attribution (e.g., "According to recent research...")
5. Advances the main argument with evidence

Return only the paragraph content.`;

      const result = await enhancedAIService.generateText(prompt, 'google/gemini-2.5-flash', {
        maxTokens: 400,
        temperature: 0.6
      });

      return {
        id: `research-${Date.now()}`,
        content: result.trim(),
        type: 'paragraph',
        confidence: 0.9,
        hasResearch: true,
        citations: searchResults.results.slice(0, 2).map(r => r.url),
        reasoning: 'Research-enhanced content with academic sources',
        wordCount: result.trim().split(/\s+/).length
      };
    } catch (error) {
      console.error('Error generating research suggestion:', error);
      return null;
    }
  };

  // Handle intent submission
  const handleIntentSubmit = () => {
    if (!writingIntent.trim()) {
      toast.error('Please describe what you want to write');
      return;
    }

    setShowIntentDialog(false);
    
    // Generate initial suggestions based on intent
    const context = analyzeWritingContext(content);
    generateWritingSuggestions(context, writingIntent);
    
    toast.success('Generating writing suggestions based on your intent...');
  };

  // Accept suggestion
  const handleAcceptSuggestion = (suggestion: WritingSuggestion) => {
    onContentInsert(suggestion.content, cursorPosition);
    setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));
    toast.success('Content added to document');
    
    if (suggestion.hasResearch && suggestion.citations) {
      toast.info(`Added content with ${suggestion.citations.length} research source(s)`);
    }
  };

  // Regenerate suggestions
  const handleRegenerateSuggestions = () => {
    if (writingContext) {
      generateWritingSuggestions(writingContext, writingIntent);
    }
  };

  // Effect to trigger assistant when content changes
  useEffect(() => {
    if (isEnabled) {
      triggerWritingAssistant();
    }
  }, [content, cursorPosition, triggerWritingAssistant, isEnabled]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  if (!isEnabled) {
    return (
      <div className={`fixed bottom-20 right-6 z-40 ${className}`}>
        <Card className="p-3 bg-gray-50 border-gray-200">
          <div className="flex items-center gap-2">
            <PenTool className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">AI Writing Assistant disabled</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleEnabled(true)}
              className="h-6 px-2 text-xs"
            >
              Enable
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <>
      {/* Main Assistant Card */}
      {isVisible && (
        <div className={`fixed bottom-20 right-6 z-40 ${className}`}>
          <Card className="w-96 bg-white shadow-xl border border-gray-200 rounded-xl overflow-hidden">
            <CardHeader className="pb-3 bg-gradient-to-r from-blue-50 to-purple-50 border-b">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <Wand2 className="h-5 w-5 text-blue-600" />
                  AI Writing Assistant
                </CardTitle>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowSettings(!showSettings)}
                    className="h-8 w-8 p-0"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsVisible(false)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              {/* Context Display */}
              {writingContext && (
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="outline" className="text-xs">
                    {writingContext.documentType}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {writingContext.currentSection}
                  </Badge>
                  {writingContext.needsResearch && autoResearch && (
                    <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                      <Search className="h-2 w-2 mr-1" />
                      Research
                    </Badge>
                  )}
                </div>
              )}
            </CardHeader>

            <CardContent className="p-0">
              {/* Settings Panel */}
              {showSettings && (
                <div className="p-4 border-b bg-gray-50 space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Auto Research</span>
                    <Switch
                      checked={autoResearch}
                      onCheckedChange={setAutoResearch}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Writing Style</span>
                    <select 
                      value={writingStyle}
                      onChange={(e) => setWritingStyle(e.target.value as any)}
                      className="text-xs border rounded px-2 py-1"
                    >
                      <option value="academic">Academic</option>
                      <option value="creative">Creative</option>
                      <option value="technical">Technical</option>
                      <option value="casual">Casual</option>
                    </select>
                  </div>
                </div>
              )}

              {/* Writing Intent */}
              {writingIntent && (
                <div className="p-3 border-b bg-blue-50">
                  <div className="text-xs font-medium text-blue-800 mb-1">Writing Goal:</div>
                  <div className="text-sm text-blue-700">{writingIntent}</div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowIntentDialog(true)}
                    className="h-6 px-2 text-xs mt-1"
                  >
                    Edit
                  </Button>
                </div>
              )}

              {/* Suggestions Section */}
              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium">Writing Suggestions</span>
                    {suggestions.length > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        {suggestions.length}
                      </Badge>
                    )}
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleRegenerateSuggestions}
                      disabled={isGenerating}
                      className="h-6 w-6 p-0"
                    >
                      <RefreshCw className={`h-3 w-3 ${isGenerating ? 'animate-spin' : ''}`} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSuggestionsExpanded(!suggestionsExpanded)}
                      className="h-6 w-6 p-0"
                    >
                      {suggestionsExpanded ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
                    </Button>
                  </div>
                </div>

                {suggestionsExpanded && (
                  <ScrollArea className="max-h-80">
                    {isGenerating ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                        <span className="ml-2 text-sm text-gray-600">Generating suggestions...</span>
                      </div>
                    ) : suggestions.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <PenTool className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                        <div className="text-sm">No suggestions available</div>
                        <div className="text-xs text-gray-400 mt-1">Start writing to get AI assistance</div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {suggestions.map((suggestion) => (
                          <Card key={suggestion.id} className="p-3 hover:bg-gray-50 transition-colors border">
                            <div className="space-y-2">
                              {/* Suggestion Header */}
                              <div className="flex items-start justify-between">
                                <div className="flex items-center gap-2">
                                  <Badge 
                                    variant="secondary" 
                                    className={`text-xs ${
                                      suggestion.hasResearch ? 'bg-green-100 text-green-700' :
                                      suggestion.type === 'transition' ? 'bg-blue-100 text-blue-700' :
                                      'bg-purple-100 text-purple-700'
                                    }`}
                                  >
                                    {suggestion.hasResearch && <Search className="h-2 w-2 mr-1" />}
                                    {suggestion.type}
                                  </Badge>
                                  <span className="text-xs text-gray-500">
                                    {suggestion.wordCount} words
                                  </span>
                                  <span className="text-xs text-gray-500">
                                    {Math.round(suggestion.confidence * 100)}%
                                  </span>
                                </div>
                              </div>

                              {/* Suggestion Content */}
                              <div className="text-sm text-gray-800 leading-relaxed">
                                {suggestion.content}
                              </div>

                              {/* Reasoning */}
                              <div className="text-xs text-gray-500 italic">
                                {suggestion.reasoning}
                              </div>

                              {/* Actions */}
                              <div className="flex items-center justify-between">
                                <div className="flex gap-1">
                                  <Button
                                    variant="default"
                                    size="sm"
                                    onClick={() => handleAcceptSuggestion(suggestion)}
                                    className="h-7 px-3 text-xs bg-blue-600 hover:bg-blue-700"
                                  >
                                    <Check className="h-3 w-3 mr-1" />
                                    Accept
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setSuggestions(prev => prev.filter(s => s.id !== suggestion.id))}
                                    className="h-7 px-3 text-xs"
                                  >
                                    <X className="h-3 w-3 mr-1" />
                                    Dismiss
                                  </Button>
                                </div>
                                
                                {suggestion.citations && (
                                  <div className="flex items-center gap-1 text-xs text-blue-600">
                                    <Quote className="h-3 w-3" />
                                    <span>{suggestion.citations.length} sources</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </Card>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Writing Intent Dialog */}
      {showIntentDialog && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Target className="h-5 w-5 text-blue-600" />
                What do you want to write today?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                value={writingIntent}
                onChange={(e) => setWritingIntent(e.target.value)}
                placeholder="Describe your writing goal, topic, or what you want to accomplish..."
                className="min-h-[100px]"
                autoFocus
              />
              <div className="text-xs text-gray-500">
                Examples: "A research paper on climate change impacts", "An introduction to machine learning", "A creative story about space exploration"
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowIntentDialog(false);
                    setHasShownIntentDialog(true);
                  }}
                  className="flex-1"
                >
                  Skip
                </Button>
                <Button
                  onClick={handleIntentSubmit}
                  disabled={!writingIntent.trim()}
                  className="flex-1"
                >
                  Start Writing
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
}
