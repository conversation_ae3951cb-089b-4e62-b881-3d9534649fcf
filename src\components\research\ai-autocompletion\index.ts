/**
 * AI Autocompletion Module Exports
 * Centralized exports for all AI autocompletion components and services
 */

// Core service
export {
  aiAutocompletionService,
  AIAutocompletionService
} from './ai-autocompletion.service';

// Performance optimization
export {
  performanceOptimizer,
  PerformanceOptimizer
} from './performance-optimizer';

export type {
  PerformanceMetrics,
  CacheEntry
} from './performance-optimizer';

// Type definitions
export type {
  AutocompletionSuggestion,
  CitationReference,
  WritingContext,
  AutocompletionOptions
} from './ai-autocompletion.service';

// Components
export { AIAutocompletionProvider } from './AIAutocompletionProvider';
export { InlineSuggestionDisplay, inlineSuggestionStyles } from './InlineSuggestionDisplay';
export { CitationManager } from './CitationManager';
export {
  EnhancedRichTextEditor,
  type EnhancedRichTextEditorRef
} from './EnhancedRichTextEditor';

// New Dual AI System
export { InlineAutocompletion } from './InlineAutocompletion';
export { AISuggestionPanel } from './AISuggestionPanel';
export {
  DualAIEditor,
  type DualAIEditorRef
} from './DualAIEditor';
export { DualAIDemo } from './DualAIDemo';

// Utility functions for integration
export const autocompletionUtils = {
  /**
   * Initialize autocompletion styles in the document
   */
  initializeStyles: () => {
    const styleId = 'ai-autocompletion-styles';
    if (document.getElementById(styleId)) return;
    
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      @keyframes animate-in {
        from {
          opacity: 0;
          transform: translateY(-100%) scale(0.95);
        }
        to {
          opacity: 1;
          transform: translateY(-100%) scale(1);
        }
      }

      @keyframes animate-accept {
        from {
          opacity: 1;
          transform: translateY(-100%) scale(1);
        }
        to {
          opacity: 0;
          transform: translateY(-100%) scale(1.05) translateX(20px);
        }
      }

      @keyframes animate-reject {
        from {
          opacity: 1;
          transform: translateY(-100%) scale(1);
        }
        to {
          opacity: 0;
          transform: translateY(-100%) scale(0.95) translateX(-20px);
        }
      }

      .animate-in {
        animation: animate-in 0.3s ease-out;
      }

      .animate-accept {
        animation: animate-accept 0.2s ease-in;
      }

      .animate-reject {
        animation: animate-reject 0.2s ease-in;
      }

      /* Suggestion preview styles */
      .ai-suggestion-preview {
        background-color: rgba(59, 130, 246, 0.1);
        border-left: 2px solid #3b82f6;
        padding: 0.5rem;
        margin: 0.25rem 0;
        border-radius: 0.25rem;
        opacity: 0.8;
        transition: opacity 0.2s ease;
      }

      .ai-suggestion-preview:hover {
        opacity: 1;
      }

      /* Citation highlight styles */
      .citation-highlight {
        background-color: rgba(34, 197, 94, 0.1);
        border-bottom: 1px dotted #22c55e;
        cursor: pointer;
      }

      .citation-highlight:hover {
        background-color: rgba(34, 197, 94, 0.2);
      }
    `;
    document.head.appendChild(style);
  },

  /**
   * Clean up autocompletion styles
   */
  cleanupStyles: () => {
    const style = document.getElementById('ai-autocompletion-styles');
    if (style) {
      style.remove();
    }
  },

  /**
   * Format citation for display
   */
  formatCitationForDisplay: (citation: CitationReference, style: 'APA' | 'MLA' = 'APA'): string => {
    const { title, authors, year, url, source } = citation;
    
    if (style === 'APA') {
      const authorStr = authors && authors.length > 0 
        ? authors.length === 1 
          ? authors[0]
          : authors.length === 2
            ? `${authors[0]} & ${authors[1]}`
            : `${authors[0]} et al.`
        : 'Unknown Author';
      
      const yearStr = year ? `(${year})` : '(n.d.)';
      const titleStr = title;
      const sourceStr = source || url;
      
      return `${authorStr} ${yearStr}. ${titleStr}. Retrieved from ${sourceStr}`;
    } else { // MLA
      const authorStr = authors && authors.length > 0 
        ? authors.length === 1 
          ? authors[0]
          : authors.length === 2
            ? `${authors[0]} and ${authors[1]}`
            : `${authors[0]} et al.`
        : 'Unknown Author';
      
      const titleStr = `"${title}"`;
      const sourceStr = source || new URL(url).hostname;
      const yearStr = year ? `, ${year}` : '';
      
      return `${authorStr}. ${titleStr} ${sourceStr}${yearStr}. Web.`;
    }
  },

  /**
   * Generate in-text citation
   */
  generateInTextCitation: (citation: CitationReference, style: 'APA' | 'MLA' = 'APA'): string => {
    const { authors, year } = citation;
    
    if (style === 'APA') {
      const authorStr = authors && authors.length > 0 
        ? authors.length === 1 
          ? authors[0].split(' ').pop() // Last name only
          : authors.length === 2
            ? `${authors[0].split(' ').pop()} & ${authors[1].split(' ').pop()}`
            : `${authors[0].split(' ').pop()} et al.`
        : 'Unknown';
      
      const yearStr = year ? year : 'n.d.';
      return `(${authorStr}, ${yearStr})`;
    } else { // MLA
      const authorStr = authors && authors.length > 0 
        ? authors[0].split(' ').pop() // Last name only
        : 'Unknown';
      
      return `(${authorStr})`;
    }
  },

  /**
   * Extract text content from HTML
   */
  extractTextFromHTML: (html: string): string => {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
  },

  /**
   * Calculate reading time for content
   */
  calculateReadingTime: (content: string): number => {
    const text = typeof content === 'string' ? content : autocompletionUtils.extractTextFromHTML(content);
    const words = text.trim().split(/\s+/).length;
    const wordsPerMinute = 200; // Average reading speed
    return Math.ceil(words / wordsPerMinute);
  },

  /**
   * Validate citation data
   */
  validateCitation: (citation: Partial<CitationReference>): boolean => {
    return !!(citation.title && citation.url);
  },

  /**
   * Merge duplicate citations
   */
  mergeDuplicateCitations: (citations: CitationReference[]): CitationReference[] => {
    const seen = new Set<string>();
    return citations.filter(citation => {
      const key = citation.url.toLowerCase();
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }
};

// Default configuration
export const defaultAutocompletionConfig: AutocompletionOptions = {
  maxSuggestions: 2,
  enableResearch: true,
  citationStyle: 'APA',
  responseTime: 'balanced',
  contextWindow: 1000
};
