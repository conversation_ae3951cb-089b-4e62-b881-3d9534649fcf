/**
 * Demo Component for Section-Based Writing
 * Test the section-based writing functionality
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  CheckCircle, 
  Clock, 
  Target,
  BookOpen,
  FlaskConical,
  BarChart3,
  Lightbulb
} from 'lucide-react';

import { 
  SectionNavigationPanel, 
  SectionAIToolbar, 
  SectionTemplateToggle,
  sectionContextService 
} from '../index';
import { SectionWritingContext, SectionProgress } from '../types';

export function SectionWritingDemo() {
  const [context, setContext] = useState<SectionWritingContext>(sectionContextService.getContext());
  const [progress, setProgress] = useState<SectionProgress>(sectionContextService.getProgress());
  const [showNavigation, setShowNavigation] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [demoContent, setDemoContent] = useState('');

  useEffect(() => {
    const unsubscribe = sectionContextService.subscribe((newContext) => {
      setContext(newContext);
      setProgress(sectionContextService.getProgress());
    });

    return unsubscribe;
  }, []);

  const handleSectionSelect = (sectionId: string) => {
    sectionContextService.setCurrentSection(sectionId);
    console.log('Selected section:', sectionId);
  };

  const handleContentInsert = (content: string, mode: 'replace' | 'insert') => {
    if (mode === 'replace' && selectedText) {
      setDemoContent(prev => prev.replace(selectedText, content));
    } else {
      setDemoContent(prev => prev + '\n\n' + content);
    }
  };

  const handleContentGenerate = (content: string) => {
    setDemoContent(content);
  };

  const handleInitializeDemo = () => {
    sectionContextService.initializeSectionMode(
      'AI-Powered Research Paper Writing: A Comprehensive Study',
      'Computer Science'
    );
    
    sectionContextService.updatePaperMetadata({
      title: 'AI-Powered Research Paper Writing: A Comprehensive Study',
      researchField: 'Computer Science',
      keywords: ['artificial intelligence', 'research writing', 'academic tools', 'natural language processing'],
      authors: ['Demo Author']
    });

    setShowNavigation(true);
  };

  const handleExitDemo = () => {
    sectionContextService.exitSectionMode();
    setShowNavigation(false);
    setDemoContent('');
  };

  const isTemplateMode = context.writingMode === 'template';

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-6 w-6 text-blue-600" />
              <span>Section-Based Writing Demo</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-gray-600">
                  Test the section-based research paper writing functionality
                </p>
                <div className="flex items-center space-x-4">
                  <Badge variant={isTemplateMode ? 'default' : 'outline'}>
                    Mode: {isTemplateMode ? 'Template' : 'Freeform'}
                  </Badge>
                  {isTemplateMode && (
                    <Badge variant="outline">
                      {progress.completed}/{progress.totalSections} sections complete
                    </Badge>
                  )}
                </div>
              </div>
              
              <div className="space-x-2">
                {!isTemplateMode ? (
                  <Button onClick={handleInitializeDemo}>
                    <BookOpen className="h-4 w-4 mr-2" />
                    Start Demo
                  </Button>
                ) : (
                  <Button variant="outline" onClick={handleExitDemo}>
                    Exit Demo
                  </Button>
                )}
                
                {isTemplateMode && (
                  <Button 
                    variant="outline" 
                    onClick={() => setShowNavigation(!showNavigation)}
                  >
                    {showNavigation ? 'Hide' : 'Show'} Navigation
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Template Toggle Demo */}
        {isTemplateMode && (
          <Card>
            <CardHeader>
              <CardTitle>Template Controls</CardTitle>
            </CardHeader>
            <CardContent>
              <SectionTemplateToggle />
            </CardContent>
          </Card>
        )}

        {/* Progress Overview */}
        {isTemplateMode && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="h-5 w-5 text-green-600" />
                <span>Writing Progress</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{progress.completed}</div>
                  <div className="text-sm text-gray-600">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">{progress.totalSections}</div>
                  <div className="text-sm text-gray-600">Total Sections</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {progress.wordCountProgress.current.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Words Written</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">{progress.estimatedTimeRemaining}m</div>
                  <div className="text-sm text-gray-600">Est. Time Left</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* AI Toolbar Demo */}
        {isTemplateMode && context.currentSection && (
          <Card>
            <CardHeader>
              <CardTitle>Section AI Tools</CardTitle>
            </CardHeader>
            <CardContent>
              <SectionAIToolbar
                selectedText={selectedText}
                onContentInsert={handleContentInsert}
                onContentGenerate={handleContentGenerate}
              />
            </CardContent>
          </Card>
        )}

        {/* Content Area */}
        <Card>
          <CardHeader>
            <CardTitle>Demo Content Area</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select text to test replacement:
                </label>
                <textarea
                  className="w-full h-40 p-3 border border-gray-300 rounded-lg resize-none"
                  placeholder="Generated content will appear here. You can select text to test the replace functionality."
                  value={demoContent}
                  onChange={(e) => setDemoContent(e.target.value)}
                  onSelect={(e) => {
                    const target = e.target as HTMLTextAreaElement;
                    const selected = target.value.substring(target.selectionStart, target.selectionEnd);
                    setSelectedText(selected);
                  }}
                />
              </div>
              
              {selectedText && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="text-sm font-medium text-blue-800 mb-1">Selected Text:</div>
                  <div className="text-sm text-blue-700">"{selectedText}"</div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Current Section Info */}
        {isTemplateMode && context.currentSection && (
          <Card>
            <CardHeader>
              <CardTitle>Current Section</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">{context.currentSection}</Badge>
                  <span className="text-sm text-gray-600">
                    Status: {context.sections[context.currentSection]?.status || 'empty'}
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  Word Count: {context.sections[context.currentSection]?.wordCount || 0}
                </div>
                <div className="text-sm text-gray-600">
                  Paper: {context.paperMetadata.title}
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Section Navigation Panel */}
      <SectionNavigationPanel
        isVisible={showNavigation}
        onToggleVisibility={() => setShowNavigation(!showNavigation)}
        onSectionSelect={handleSectionSelect}
      />
    </div>
  );
}
