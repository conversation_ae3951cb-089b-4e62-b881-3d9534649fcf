/**
 * Section-Focused Editor Component
 * Provides a step-by-step section writing experience with focused UI
 */

import React, { useState, useEffect } from 'react';
import { 
  ChevronLeft, 
  ChevronRight, 
  CheckCircle, 
  Circle, 
  Eye,
  EyeOff,
  Settings,
  Target,
  Clock,
  BookOpen,
  ArrowLeft,
  ArrowRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

import { sectionContextService } from '../services/section-context.service';
import { RESEARCH_SECTIONS, SECTION_WORD_TARGETS } from '../constants';
import { 
  SectionWritingContext, 
  ResearchSection,
  SectionContent 
} from '../types';
import { SectionSpecificTools } from './SectionSpecificTools';

interface SectionFocusedEditorProps {
  content: string;
  onContentChange: (content: string) => void;
  selectedText?: string;
  className?: string;
}

export function SectionFocusedEditor({
  content,
  onContentChange,
  selectedText,
  className = ''
}: SectionFocusedEditorProps) {
  const [context, setContext] = useState<SectionWritingContext>(sectionContextService.getContext());
  const [showAllSections, setShowAllSections] = useState(false);
  const [isCompactMode, setIsCompactMode] = useState(false);

  useEffect(() => {
    const unsubscribe = sectionContextService.subscribe(setContext);
    return unsubscribe;
  }, []);

  const currentSectionIndex = RESEARCH_SECTIONS.findIndex(
    s => s.id === context.currentSection
  );
  
  const currentSection = RESEARCH_SECTIONS[currentSectionIndex];
  const canGoPrevious = currentSectionIndex > 0;
  const canGoNext = currentSectionIndex < RESEARCH_SECTIONS.length - 1;

  const handlePreviousSection = () => {
    if (canGoPrevious) {
      const prevSection = RESEARCH_SECTIONS[currentSectionIndex - 1];
      sectionContextService.setCurrentSection(prevSection.id);
    }
  };

  const handleNextSection = () => {
    if (canGoNext) {
      const nextSection = RESEARCH_SECTIONS[currentSectionIndex + 1];
      sectionContextService.setCurrentSection(nextSection.id);
    }
  };

  const handleSectionSelect = (sectionId: string) => {
    sectionContextService.setCurrentSection(sectionId);
  };

  const getSectionStatus = (section: ResearchSection): SectionContent['status'] => {
    return context.sections[section.id]?.status || 'empty';
  };

  const getSectionWordCount = (section: ResearchSection): number => {
    return context.sections[section.id]?.wordCount || 0;
  };

  const getStatusIcon = (status: SectionContent['status']) => {
    switch (status) {
      case 'complete':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'review':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'draft':
        return <Circle className="h-4 w-4 text-blue-500 fill-current" />;
      default:
        return <Circle className="h-4 w-4 text-gray-300" />;
    }
  };

  const getWordCountProgress = (section: ResearchSection): number => {
    const wordCount = getSectionWordCount(section);
    const target = SECTION_WORD_TARGETS[section.id as keyof typeof SECTION_WORD_TARGETS];
    if (!target) return 0;
    return Math.min((wordCount / target.target) * 100, 100);
  };

  if (context.writingMode !== 'template') {
    return null;
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Section Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Section Navigation */}
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePreviousSection}
                disabled={!canGoPrevious}
                className="h-8 w-8 p-0"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <div className="flex items-center space-x-3">
                {currentSection && (
                  <>
                    <div className={`p-2 rounded-lg ${currentSection.color}`}>
                      <currentSection.icon className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{currentSection.name}</h3>
                      <p className="text-sm text-gray-600">{currentSection.description}</p>
                    </div>
                  </>
                )}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleNextSection}
                disabled={!canGoNext}
                className="h-8 w-8 p-0"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Section Progress */}
            {currentSection && (
              <div className="flex items-center space-x-3">
                <Badge variant="outline" className="text-xs">
                  {currentSectionIndex + 1} of {RESEARCH_SECTIONS.length}
                </Badge>
                
                <div className="flex items-center space-x-2">
                  {getStatusIcon(getSectionStatus(currentSection))}
                  <span className="text-sm text-gray-600">
                    {getSectionWordCount(currentSection)} words
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAllSections(!showAllSections)}
                    className="h-8"
                  >
                    {showAllSections ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{showAllSections ? 'Hide' : 'Show'} all sections</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsCompactMode(!isCompactMode)}
                    className="h-8"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Toggle compact mode</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* Section Progress Bar */}
        {currentSection && !isCompactMode && (
          <div className="mt-3">
            <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
              <span>Progress</span>
              <span>
                {getSectionWordCount(currentSection)} / {SECTION_WORD_TARGETS[currentSection.id as keyof typeof SECTION_WORD_TARGETS]?.target || 0} words
              </span>
            </div>
            <Progress value={getWordCountProgress(currentSection)} className="h-2" />
          </div>
        )}
      </div>

      {/* All Sections Overview */}
      {showAllSections && (
        <div className="border-b border-gray-200 p-4 bg-gray-50">
          <h4 className="font-medium text-gray-900 mb-3">All Sections</h4>
          <div className="grid grid-cols-3 gap-2">
            {RESEARCH_SECTIONS.map((section, index) => {
              const status = getSectionStatus(section);
              const isActive = section.id === context.currentSection;
              
              return (
                <button
                  key={section.id}
                  onClick={() => handleSectionSelect(section.id)}
                  className={`
                    flex items-center space-x-2 p-2 rounded-lg border text-left transition-all
                    ${isActive 
                      ? 'bg-blue-50 border-blue-200 shadow-sm' 
                      : 'bg-white border-gray-200 hover:bg-gray-50'
                    }
                  `}
                >
                  <div className={`p-1 rounded ${section.color}`}>
                    <section.icon className="h-3 w-3 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-xs font-medium text-gray-900 truncate">
                      {section.name}
                    </div>
                    <div className="flex items-center space-x-1">
                      {getStatusIcon(status)}
                      <span className="text-xs text-gray-600">
                        {getSectionWordCount(section)}w
                      </span>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Section-Specific Tools */}
      {currentSection && (
        <div className="p-4">
          <SectionSpecificTools
            section={currentSection}
            content={content}
            selectedText={selectedText}
            onContentChange={onContentChange}
            isCompactMode={isCompactMode}
          />
        </div>
      )}

      {/* Navigation Footer */}
      <div className="border-t border-gray-200 p-3 bg-gray-50">
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePreviousSection}
            disabled={!canGoPrevious}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Previous</span>
          </Button>

          <div className="text-xs text-gray-600">
            Step {currentSectionIndex + 1} of {RESEARCH_SECTIONS.length}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={handleNextSection}
            disabled={!canGoNext}
            className="flex items-center space-x-2"
          >
            <span>Next</span>
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
