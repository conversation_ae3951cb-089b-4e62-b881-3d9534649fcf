/**
 * AI Autocompletion Service
 * Handles real-time text suggestions, context analysis, and citation management
 */

import { enhancedAIService } from '../paper-generator/enhanced-ai.service';
import { tavilySearchService } from '../research-search/services/tavily-search.service';
import { performanceOptimizer } from './performance-optimizer';

export interface AutocompletionSuggestion {
  id: string;
  content: string;
  type: 'sentence' | 'paragraph' | 'continuation';
  confidence: number;
  hasResearch: boolean;
  citations?: CitationReference[];
  reasoning: string;
  wordCount: number;
  timestamp: Date;
}

export interface CitationReference {
  id: string;
  title: string;
  url: string;
  authors?: string[];
  year?: number;
  source: string;
  snippet: string;
  format: 'APA' | 'MLA';
}

export interface WritingContext {
  documentType: 'academic' | 'creative' | 'technical' | 'casual';
  currentSection: 'introduction' | 'body' | 'conclusion' | 'unknown';
  tone: 'formal' | 'informal' | 'technical' | 'conversational';
  topicKeywords: string[];
  recentContent: string;
  needsResearch: boolean;
  writingFlow: 'starting' | 'continuing' | 'transitioning' | 'concluding';
}

export interface AutocompletionOptions {
  maxSuggestions: number;
  enableResearch: boolean;
  citationStyle: 'APA' | 'MLA';
  responseTime: 'fast' | 'balanced' | 'thorough';
  contextWindow: number;
}

export class AIAutocompletionService {
  private static instance: AIAutocompletionService;
  private suggestionCache = new Map<string, AutocompletionSuggestion[]>();
  private citationStore = new Map<string, CitationReference>();
  private contextHistory: WritingContext[] = [];
  private isGenerating = false;
  private lastGenerationTime = 0;
  private debounceTimeout: NodeJS.Timeout | null = null;

  private defaultOptions: AutocompletionOptions = {
    maxSuggestions: 2,
    enableResearch: true,
    citationStyle: 'APA',
    responseTime: 'balanced',
    contextWindow: 1000
  };

  static getInstance(): AIAutocompletionService {
    if (!AIAutocompletionService.instance) {
      AIAutocompletionService.instance = new AIAutocompletionService();
    }
    return AIAutocompletionService.instance;
  }

  /**
   * Generate real-time autocompletion suggestions
   */
  async generateSuggestions(
    content: string,
    cursorPosition: number,
    options: Partial<AutocompletionOptions> = {}
  ): Promise<AutocompletionSuggestion[]> {
    const opts = { ...this.defaultOptions, ...options };

    // Prevent excessive API calls
    if (this.isGenerating || Date.now() - this.lastGenerationTime < 1000) {
      return [];
    }

    const context = this.analyzeWritingContext(content, cursorPosition, opts.contextWindow);
    const cacheKey = performanceOptimizer.generateCacheKey(content, cursorPosition, context, opts);

    // Check performance optimizer cache first
    const cachedSuggestions = performanceOptimizer.getCachedSuggestions(cacheKey);
    if (cachedSuggestions) {
      return cachedSuggestions;
    }

    // Use performance optimizer's debounced execution
    const debouncedGeneration = performanceOptimizer.debounce(
      `suggestions-${cacheKey}`,
      async () => {
        this.isGenerating = true;
        this.lastGenerationTime = Date.now();

        try {
          const suggestions = await performanceOptimizer.queueRequest(async () => {
            return await this.generateContextualSuggestions(context, opts);
          });

          // Cache results using performance optimizer
          performanceOptimizer.cacheSuggestions(cacheKey, suggestions);
          this.contextHistory.push(context);

          // Limit context history
          if (this.contextHistory.length > 20) {
            this.contextHistory.shift();
          }

          return suggestions;
        } catch (error) {
          console.error('Error generating autocompletion suggestions:', error);
          return [];
        } finally {
          this.isGenerating = false;
        }
      },
      opts.responseTime === 'fast' ? 500 : opts.responseTime === 'balanced' ? 1000 : 1500
    );

    return await debouncedGeneration();
  }

  /**
   * Analyze writing context from document content
   */
  private analyzeWritingContext(
    content: string,
    cursorPosition: number,
    contextWindow: number
  ): WritingContext {
    const beforeCursor = content.slice(0, cursorPosition);
    const afterCursor = content.slice(cursorPosition);
    const recentContent = beforeCursor.slice(-contextWindow);
    const text = content.toLowerCase();
    const wordCount = content.split(/\s+/).length;

    // Detect document type
    let documentType: WritingContext['documentType'] = 'casual';
    if (text.includes('abstract') || text.includes('methodology') || text.includes('references')) {
      documentType = 'academic';
    } else if (text.includes('function') || text.includes('algorithm') || text.includes('implementation')) {
      documentType = 'technical';
    } else if (text.includes('once upon') || text.includes('character') || text.includes('story')) {
      documentType = 'creative';
    }

    // Detect current section
    let currentSection: WritingContext['currentSection'] = 'unknown';
    if (wordCount < 100) {
      currentSection = 'introduction';
    } else if (text.includes('conclusion') || text.includes('in summary') || text.includes('to conclude')) {
      currentSection = 'conclusion';
    } else {
      currentSection = 'body';
    }

    // Determine writing flow
    let writingFlow: WritingContext['writingFlow'] = 'continuing';
    if (wordCount < 50) {
      writingFlow = 'starting';
    } else if (beforeCursor.trim().endsWith('.') || beforeCursor.trim().endsWith('!') || beforeCursor.trim().endsWith('?')) {
      writingFlow = 'transitioning';
    } else if (currentSection === 'conclusion') {
      writingFlow = 'concluding';
    }

    // Determine if research is needed
    const needsResearch = documentType === 'academic' || 
                         text.includes('research') || 
                         text.includes('study') || 
                         text.includes('according to') ||
                         text.includes('evidence');

    // Extract topic keywords
    const topicKeywords = this.extractKeywords(content);

    return {
      documentType,
      currentSection,
      tone: documentType === 'academic' ? 'formal' : 'conversational',
      topicKeywords,
      recentContent,
      needsResearch,
      writingFlow
    };
  }

  /**
   * Extract relevant keywords from content
   */
  private extractKeywords(text: string): string[] {
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 4)
      .filter(word => !['that', 'this', 'with', 'from', 'they', 'have', 'been', 'were', 'will', 'would', 'could', 'should'].includes(word));
    
    // Get unique words and return top 5
    const uniqueWords = [...new Set(words)];
    return uniqueWords.slice(0, 5);
  }

  /**
   * Generate contextual suggestions based on writing context
   */
  private async generateContextualSuggestions(
    context: WritingContext,
    options: AutocompletionOptions
  ): Promise<AutocompletionSuggestion[]> {
    const suggestions: AutocompletionSuggestion[] = [];

    try {
      // Generate different types of suggestions based on context
      const suggestionPromises: Promise<AutocompletionSuggestion | null>[] = [];

      // Always generate a continuation suggestion
      suggestionPromises.push(this.generateContinuationSuggestion(context, options));

      // Generate sentence completion if mid-sentence
      if (!context.recentContent.trim().endsWith('.') && !context.recentContent.trim().endsWith('!') && !context.recentContent.trim().endsWith('?')) {
        suggestionPromises.push(this.generateSentenceCompletion(context, options));
      }

      // Generate research-enhanced suggestion if needed
      if (context.needsResearch && options.enableResearch && tavilySearchService.isConfigured()) {
        suggestionPromises.push(this.generateResearchEnhancedSuggestion(context, options));
      }

      const results = await Promise.allSettled(suggestionPromises);
      
      results.forEach((result) => {
        if (result.status === 'fulfilled' && result.value) {
          suggestions.push(result.value);
        }
      });

      // Limit to max suggestions
      return suggestions.slice(0, options.maxSuggestions);
    } catch (error) {
      console.error('Error generating contextual suggestions:', error);
      return [];
    }
  }

  /**
   * Generate continuation suggestion
   */
  private async generateContinuationSuggestion(
    context: WritingContext,
    options: AutocompletionOptions
  ): Promise<AutocompletionSuggestion | null> {
    const prompt = `Continue this ${context.documentType} writing naturally and coherently:

Recent content: "${context.recentContent}"

Writing context:
- Document type: ${context.documentType}
- Current section: ${context.currentSection}
- Tone: ${context.tone}
- Writing flow: ${context.writingFlow}
- Topic keywords: ${context.topicKeywords.join(', ')}

Generate ${context.writingFlow === 'starting' ? 'an opening sentence or two' : 
           context.writingFlow === 'concluding' ? 'a concluding statement' : 
           'the next 1-2 sentences'} that:
1. Flow naturally from the existing content
2. Maintain the established tone and style
3. Advance the main argument or narrative
4. Use appropriate ${context.documentType} language
5. Provide substantive content, not filler

Return only the text content, no explanations.`;

    try {
      const result = await enhancedAIService.generateText(prompt, 'google/gemini-2.5-flash', {
        maxTokens: 200,
        temperature: 0.7
      });

      return {
        id: `continuation-${Date.now()}`,
        content: result.trim(),
        type: 'continuation',
        confidence: 0.85,
        hasResearch: false,
        reasoning: `Natural continuation for ${context.writingFlow} flow`,
        wordCount: result.trim().split(/\s+/).length,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Error generating continuation suggestion:', error);
      return null;
    }
  }

  /**
   * Generate sentence completion
   */
  private async generateSentenceCompletion(
    context: WritingContext,
    options: AutocompletionOptions
  ): Promise<AutocompletionSuggestion | null> {
    const lastSentence = context.recentContent.split(/[.!?]/).pop()?.trim() || '';
    
    if (!lastSentence || lastSentence.length < 10) return null;

    const prompt = `Complete this ${context.documentType} sentence naturally:

Incomplete sentence: "${lastSentence}"

Context: ${context.documentType} writing, ${context.tone} tone
Topic: ${context.topicKeywords.slice(0, 2).join(', ')}

Complete the sentence in a way that:
1. Makes grammatical sense
2. Maintains the established tone
3. Provides meaningful content
4. Fits the document type

Return only the completion (the rest of the sentence), no explanations.`;

    try {
      const result = await enhancedAIService.generateText(prompt, 'google/gemini-2.5-flash', {
        maxTokens: 100,
        temperature: 0.6
      });

      return {
        id: `sentence-${Date.now()}`,
        content: result.trim(),
        type: 'sentence',
        confidence: 0.8,
        hasResearch: false,
        reasoning: 'Sentence completion based on context',
        wordCount: result.trim().split(/\s+/).length,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Error generating sentence completion:', error);
      return null;
    }
  }

  /**
   * Generate research-enhanced suggestion with citations
   */
  private async generateResearchEnhancedSuggestion(
    context: WritingContext,
    options: AutocompletionOptions
  ): Promise<AutocompletionSuggestion | null> {
    try {
      // Create search query from context
      const searchQuery = context.topicKeywords.slice(0, 3).join(' ');
      
      if (!searchQuery.trim()) return null;

      // Perform academic search
      const searchResults = await tavilySearchService.searchAcademic(searchQuery, {
        maxResults: 2,
        searchDepth: 'basic',
        includeAnswer: true
      });

      if (!searchResults.results.length) return null;

      // Store citations
      const citations: CitationReference[] = searchResults.results.map((result, index) => {
        const citation: CitationReference = {
          id: `cite-${Date.now()}-${index}`,
          title: result.title,
          url: result.url,
          source: result.url,
          snippet: result.content.slice(0, 200),
          format: options.citationStyle
        };
        
        this.citationStore.set(citation.id, citation);
        return citation;
      });

      // Generate content with research integration
      const researchContext = searchResults.results
        .map(result => `Source: ${result.title}\nContent: ${result.content.slice(0, 300)}`)
        .join('\n\n');

      const prompt = `Write a research-informed sentence or two using these academic sources:

${researchContext}

Recent document content: "${context.recentContent}"
Topic focus: ${searchQuery}

Create 1-2 sentences that:
1. Integrate insights from the research sources
2. Maintain ${context.tone} tone
3. Flow naturally from existing content
4. Include proper attribution (e.g., "According to recent research...")
5. Advance the main argument with evidence

Return only the sentence(s), no explanations.`;

      const result = await enhancedAIService.generateText(prompt, 'google/gemini-2.5-flash', {
        maxTokens: 150,
        temperature: 0.6
      });

      return {
        id: `research-${Date.now()}`,
        content: result.trim(),
        type: 'paragraph',
        confidence: 0.9,
        hasResearch: true,
        citations,
        reasoning: 'Research-enhanced content with academic sources',
        wordCount: result.trim().split(/\s+/).length,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Error generating research suggestion:', error);
      return null;
    }
  }

  /**
   * Get performance metrics from optimizer
   */
  getPerformanceMetrics() {
    return performanceOptimizer.getMetrics();
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return performanceOptimizer.getCacheStats();
  }

  /**
   * Get stored citation by ID
   */
  getCitation(citationId: string): CitationReference | undefined {
    return this.citationStore.get(citationId);
  }

  /**
   * Get all stored citations
   */
  getAllCitations(): CitationReference[] {
    return Array.from(this.citationStore.values());
  }

  /**
   * Clear suggestion cache
   */
  clearCache(): void {
    this.suggestionCache.clear();
    performanceOptimizer.clearCache();
  }

  /**
   * Check if service is currently generating suggestions
   */
  isCurrentlyGenerating(): boolean {
    return this.isGenerating;
  }
}

export const aiAutocompletionService = AIAutocompletionService.getInstance();
