/**
 * AI Autocompletion Demo Component
 * Demonstrates the enhanced AI autocompletion features
 */

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>ap, 
  BookOpen, 
  Settings,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';
import { EnhancedRichTextEditor } from './EnhancedRichTextEditor';
import { autocompletionUtils } from './index';

export function AutocompletionDemo() {
  const [content, setContent] = useState(`# AI Autocompletion Demo

Welcome to the enhanced AI autocompletion system! This demo showcases the seamless writing experience with real-time AI suggestions.

## Getting Started

Start typing below and watch as the AI provides contextual suggestions. The system will:

1. **Analyze your writing context** - Understanding document type, tone, and topic
2. **Generate relevant suggestions** - Providing 1-2 sentence continuations or completions
3. **Integrate research sources** - Adding citations and references automatically
4. **Learn from your preferences** - Adapting to your writing style over time

## Try These Examples

**Academic Writing**: "The impact of artificial intelligence on modern education"

**Creative Writing**: "In a world where technology has advanced beyond recognition"

**Technical Documentation**: "To implement the new authentication system"

## Features to Test

- **Real-time suggestions** as you type
- **Tab to accept**, **Esc to reject** keyboard shortcuts
- **Research integration** with automatic citations
- **Context awareness** for different writing styles
- **Citation management** with clickable references

Start writing below to experience the AI autocompletion in action!

---

`);

  const [isEnabled, setIsEnabled] = useState(true);
  const [showStats, setShowStats] = useState(true);

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
  };

  const resetDemo = () => {
    setContent(`# AI Autocompletion Demo

Welcome to the enhanced AI autocompletion system! Start typing below to see AI suggestions appear in real-time.

## Your Writing Space

`);
  };

  const toggleAutocompletion = () => {
    setIsEnabled(!isEnabled);
  };

  // Initialize styles when component mounts
  React.useEffect(() => {
    autocompletionUtils.initializeStyles();
    
    return () => {
      autocompletionUtils.cleanupStyles();
    };
  }, []);

  const wordCount = autocompletionUtils.extractTextFromHTML(content).split(/\s+/).length;
  const readingTime = autocompletionUtils.calculateReadingTime(content);

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Sparkles className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-2xl">AI Autocompletion Demo</CardTitle>
                <p className="text-gray-600 mt-1">
                  Experience seamless AI-powered writing assistance with real-time suggestions
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant={isEnabled ? "default" : "secondary"} className="flex items-center gap-1">
                <Zap className="h-3 w-3" />
                {isEnabled ? 'Active' : 'Disabled'}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={toggleAutocompletion}
                className="flex items-center gap-2"
              >
                {isEnabled ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                {isEnabled ? 'Disable' : 'Enable'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={resetDemo}
                className="flex items-center gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Reset
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Stats Panel */}
      {showStats && (
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{wordCount}</div>
                <div className="text-sm text-gray-600">Words</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{readingTime}</div>
                <div className="text-sm text-gray-600">Min Read</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {isEnabled ? 'ON' : 'OFF'}
                </div>
                <div className="text-sm text-gray-600">AI Assist</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">APA</div>
                <div className="text-sm text-gray-600">Citations</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid md:grid-cols-3 gap-4">
            <div className="flex items-start gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Sparkles className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold">Real-time Suggestions</h3>
                <p className="text-sm text-gray-600">
                  AI suggestions appear as you type, providing contextual continuations
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <BookOpen className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <h3 className="font-semibold">Research Integration</h3>
                <p className="text-sm text-gray-600">
                  Automatic citation and reference management with academic sources
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Settings className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <h3 className="font-semibold">Smart Controls</h3>
                <p className="text-sm text-gray-600">
                  Use Tab to accept, Esc to reject, or click the suggestion controls
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Editor */}
      <Card>
        <CardContent className="p-0">
          <div className="border-b p-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">Enhanced Editor</h3>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  Enhanced Mode
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowStats(!showStats)}
                  className="h-6 px-2 text-xs"
                >
                  {showStats ? 'Hide' : 'Show'} Stats
                </Button>
              </div>
            </div>
          </div>
          
          <div className="p-6">
            <EnhancedRichTextEditor
              content={content}
              onChange={handleContentChange}
              enableAutocompletion={isEnabled}
              autocompletionOptions={{
                maxSuggestions: 2,
                enableResearch: true,
                citationStyle: 'APA',
                responseTime: 'balanced'
              }}
              className="min-h-[500px]"
            />
          </div>
        </CardContent>
      </Card>

      {/* Tips */}
      <Card>
        <CardContent className="pt-6">
          <h3 className="font-semibold mb-3">💡 Tips for Best Results</h3>
          <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-600">
            <ul className="space-y-2">
              <li>• Write naturally - the AI adapts to your style</li>
              <li>• Use academic language for research-enhanced suggestions</li>
              <li>• Try different document types (academic, creative, technical)</li>
            </ul>
            <ul className="space-y-2">
              <li>• Press Tab to quickly accept suggestions</li>
              <li>• Press Esc to reject and pause suggestions temporarily</li>
              <li>• Click citation links to view original sources</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
