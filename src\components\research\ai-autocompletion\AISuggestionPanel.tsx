/**
 * AI Suggestion Panel Component
 * Modern right-side panel for paragraph-level AI suggestions with trigger icon
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import { 
  Sparkles, 
  Plus, 
  RefreshCw, 
  Check, 
  X, 
  Loader2,
  Brain,
  Search,
  Quote,
  ChevronRight,
  ChevronLeft,
  Settings,
  Minimize2,
  Maximize2,
  Wand2
} from 'lucide-react';
import { toast } from 'sonner';
import { enhancedAIService } from '../paper-generator/enhanced-ai.service';
import { tavilySearchService } from '../research-search/services/tavily-search.service';
import { CitationReference } from './ai-autocompletion.service';
import { cn } from '@/lib/utils';

interface ParagraphSuggestion {
  id: string;
  content: string;
  type: 'continuation' | 'expansion' | 'research' | 'transition';
  confidence: number;
  hasResearch: boolean;
  citations?: CitationReference[];
  reasoning: string;
  wordCount: number;
  timestamp: Date;
}

interface AISuggestionPanelProps {
  content: string;
  cursorPosition: number;
  onContentInsert: (content: string, position: number) => void;
  onCitationAdd: (citation: CitationReference) => void;
  isVisible: boolean;
  onToggleVisibility: () => void;
  className?: string;
}

export function AISuggestionPanel({
  content,
  cursorPosition,
  onContentInsert,
  onCitationAdd,
  isVisible,
  onToggleVisibility,
  className = ''
}: AISuggestionPanelProps) {
  const [suggestions, setSuggestions] = useState<ParagraphSuggestion[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [customPrompt, setCustomPrompt] = useState('');
  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  const [currentSuggestionIndex, setCurrentSuggestionIndex] = useState(0);
  const [isMinimized, setIsMinimized] = useState(false);
  const [autoGenerate, setAutoGenerate] = useState(false);
  
  const lastGenerationRef = useRef(0);
  const panelRef = useRef<HTMLDivElement>(null);

  // Analyze writing context for suggestions
  const analyzeContext = useCallback((text: string, position: number) => {
    const beforeCursor = text.slice(0, position);
    const afterCursor = text.slice(position);
    const recentContent = beforeCursor.slice(-500);
    const wordCount = text.split(/\s+/).length;
    
    // Detect document type
    let documentType: 'academic' | 'creative' | 'technical' | 'casual' = 'casual';
    const lowerText = text.toLowerCase();
    if (lowerText.includes('abstract') || lowerText.includes('methodology') || lowerText.includes('references')) {
      documentType = 'academic';
    } else if (lowerText.includes('function') || lowerText.includes('algorithm') || lowerText.includes('implementation')) {
      documentType = 'technical';
    } else if (lowerText.includes('once upon') || lowerText.includes('character') || lowerText.includes('story')) {
      documentType = 'creative';
    }

    // Detect current section
    let currentSection: 'introduction' | 'body' | 'conclusion' | 'unknown' = 'unknown';
    if (wordCount < 100) {
      currentSection = 'introduction';
    } else if (lowerText.includes('conclusion') || lowerText.includes('in summary') || lowerText.includes('to conclude')) {
      currentSection = 'conclusion';
    } else {
      currentSection = 'body';
    }

    // Extract topic keywords
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 4)
      .filter(word => !['that', 'this', 'with', 'from', 'they', 'have', 'been', 'were', 'will', 'would', 'could', 'should'].includes(word));
    
    const topicKeywords = [...new Set(words)].slice(0, 5);

    return {
      documentType,
      currentSection,
      recentContent,
      topicKeywords,
      needsResearch: documentType === 'academic' || lowerText.includes('research') || lowerText.includes('study'),
      hasContentAfter: afterCursor.trim().length > 0
    };
  }, []);

  // Generate paragraph suggestions
  const generateSuggestions = useCallback(async (customPromptText?: string) => {
    if (isGenerating || Date.now() - lastGenerationRef.current < 2000) return;

    setIsGenerating(true);
    lastGenerationRef.current = Date.now();

    try {
      const context = analyzeContext(content, cursorPosition);
      const newSuggestions: ParagraphSuggestion[] = [];

      if (customPromptText) {
        // Generate custom suggestion
        const customSuggestion = await generateCustomSuggestion(customPromptText, context);
        if (customSuggestion) newSuggestions.push(customSuggestion);
      } else {
        // Generate different types of suggestions
        const suggestionPromises = [
          generateContinuationSuggestion(context),
          generateExpansionSuggestion(context)
        ];

        if (context.needsResearch && tavilySearchService.isConfigured()) {
          suggestionPromises.push(generateResearchSuggestion(context));
        }

        if (!context.hasContentAfter) {
          suggestionPromises.push(generateTransitionSuggestion(context));
        }

        const results = await Promise.allSettled(suggestionPromises);
        results.forEach(result => {
          if (result.status === 'fulfilled' && result.value) {
            newSuggestions.push(result.value);
          }
        });
      }

      setSuggestions(newSuggestions);
      setCurrentSuggestionIndex(0);
      
      if (newSuggestions.length === 0) {
        toast.info('No suggestions generated. Try a different context or custom prompt.');
      }
    } catch (error) {
      console.error('Error generating suggestions:', error);
      toast.error('Failed to generate suggestions');
    } finally {
      setIsGenerating(false);
    }
  }, [content, cursorPosition, isGenerating, analyzeContext]);

  // Generate continuation suggestion
  const generateContinuationSuggestion = async (context: any): Promise<ParagraphSuggestion | null> => {
    const prompt = `Continue this ${context.documentType} writing with a well-structured paragraph:

Recent content: "${context.recentContent}"

Generate a paragraph (100-200 words) that:
1. Flows naturally from the existing content
2. Maintains the established tone and style
3. Advances the main argument or narrative
4. Uses appropriate ${context.documentType} language
5. Provides substantive content, not filler

Return only the paragraph content.`;

    try {
      const result = await enhancedAIService.generateText(prompt, 'google/gemini-2.5-flash', {
        maxTokens: 300,
        temperature: 0.7
      });

      return {
        id: `continuation-${Date.now()}`,
        content: result.trim(),
        type: 'continuation',
        confidence: 0.85,
        hasResearch: false,
        reasoning: 'Natural continuation of existing content',
        wordCount: result.trim().split(/\s+/).length,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Error generating continuation:', error);
      return null;
    }
  };

  // Generate expansion suggestion
  const generateExpansionSuggestion = async (context: any): Promise<ParagraphSuggestion | null> => {
    const prompt = `Expand on this ${context.documentType} content with additional depth:

Recent content: "${context.recentContent}"
Topic keywords: ${context.topicKeywords.join(', ')}

Generate a paragraph (150-250 words) that:
1. Adds depth and detail to the existing content
2. Provides examples, evidence, or elaboration
3. Maintains academic rigor and clarity
4. Connects to the main themes
5. Offers new insights or perspectives

Return only the paragraph content.`;

    try {
      const result = await enhancedAIService.generateText(prompt, 'google/gemini-2.5-flash', {
        maxTokens: 350,
        temperature: 0.6
      });

      return {
        id: `expansion-${Date.now()}`,
        content: result.trim(),
        type: 'expansion',
        confidence: 0.8,
        hasResearch: false,
        reasoning: 'Expanded content with additional depth',
        wordCount: result.trim().split(/\s+/).length,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Error generating expansion:', error);
      return null;
    }
  };

  // Generate research-enhanced suggestion
  const generateResearchSuggestion = async (context: any): Promise<ParagraphSuggestion | null> => {
    try {
      const searchQuery = context.topicKeywords.slice(0, 3).join(' ');
      if (!searchQuery.trim()) return null;

      const searchResults = await tavilySearchService.searchAcademic(searchQuery, {
        maxResults: 3,
        searchDepth: 'basic',
        includeAnswer: true
      });

      if (!searchResults.results.length) return null;

      const citations: CitationReference[] = searchResults.results.map((result, index) => ({
        id: `cite-${Date.now()}-${index}`,
        title: result.title,
        url: result.url,
        source: result.url,
        snippet: result.content.slice(0, 200),
        format: 'APA'
      }));

      const researchContext = searchResults.results
        .map(result => `Source: ${result.title}\nContent: ${result.content.slice(0, 300)}`)
        .join('\n\n');

      const prompt = `Write a research-informed paragraph using these academic sources:

${researchContext}

Recent document content: "${context.recentContent}"
Topic focus: ${searchQuery}

Create a paragraph (150-250 words) that:
1. Integrates insights from the research sources
2. Maintains academic tone and rigor
3. Flows naturally from existing content
4. Includes proper attribution
5. Advances the main argument with evidence

Return only the paragraph content.`;

      const result = await enhancedAIService.generateText(prompt, 'google/gemini-2.5-flash', {
        maxTokens: 400,
        temperature: 0.6
      });

      return {
        id: `research-${Date.now()}`,
        content: result.trim(),
        type: 'research',
        confidence: 0.9,
        hasResearch: true,
        citations,
        reasoning: 'Research-enhanced content with academic sources',
        wordCount: result.trim().split(/\s+/).length,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Error generating research suggestion:', error);
      return null;
    }
  };

  // Generate transition suggestion
  const generateTransitionSuggestion = async (context: any): Promise<ParagraphSuggestion | null> => {
    const prompt = `Create a transitional paragraph for this ${context.documentType} writing:

Recent content: "${context.recentContent}"

Generate a paragraph (80-150 words) that:
1. Provides smooth transition to the next section
2. Summarizes key points made so far
3. Sets up for conclusion or next major point
4. Maintains the established tone
5. Uses appropriate transition words

Return only the paragraph content.`;

    try {
      const result = await enhancedAIService.generateText(prompt, 'google/gemini-2.5-flash', {
        maxTokens: 200,
        temperature: 0.6
      });

      return {
        id: `transition-${Date.now()}`,
        content: result.trim(),
        type: 'transition',
        confidence: 0.75,
        hasResearch: false,
        reasoning: 'Transitional content for smooth flow',
        wordCount: result.trim().split(/\s+/).length,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Error generating transition:', error);
      return null;
    }
  };

  // Generate custom suggestion
  const generateCustomSuggestion = async (promptText: string, context: any): Promise<ParagraphSuggestion | null> => {
    const prompt = `Based on this request: "${promptText}"

Recent content: "${context.recentContent}"
Document type: ${context.documentType}

Generate a paragraph (100-300 words) that:
1. Addresses the specific request
2. Flows naturally from existing content
3. Maintains appropriate tone and style
4. Provides valuable content
5. Fits the document context

Return only the paragraph content.`;

    try {
      const result = await enhancedAIService.generateText(prompt, 'google/gemini-2.5-flash', {
        maxTokens: 400,
        temperature: 0.7
      });

      return {
        id: `custom-${Date.now()}`,
        content: result.trim(),
        type: 'continuation',
        confidence: 0.8,
        hasResearch: false,
        reasoning: `Custom suggestion: ${promptText}`,
        wordCount: result.trim().split(/\s+/).length,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Error generating custom suggestion:', error);
      return null;
    }
  };

  // Handle suggestion acceptance
  const handleAcceptSuggestion = useCallback((suggestion: ParagraphSuggestion) => {
    onContentInsert(suggestion.content, cursorPosition);
    
    if (suggestion.citations) {
      suggestion.citations.forEach(citation => {
        onCitationAdd(citation);
      });
      toast.success(`Content added with ${suggestion.citations.length} citation(s)`);
    } else {
      toast.success('Content added to document');
    }

    // Remove accepted suggestion
    setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));
    if (currentSuggestionIndex >= suggestions.length - 1) {
      setCurrentSuggestionIndex(Math.max(0, suggestions.length - 2));
    }
  }, [cursorPosition, onContentInsert, onCitationAdd, currentSuggestionIndex, suggestions.length]);

  // Handle custom prompt submission
  const handleCustomPromptSubmit = useCallback(() => {
    if (!customPrompt.trim()) {
      toast.error('Please enter a prompt');
      return;
    }
    
    generateSuggestions(customPrompt);
    setCustomPrompt('');
    setShowCustomPrompt(false);
  }, [customPrompt, generateSuggestions]);

  // Auto-generate on content change (disabled by default)
  useEffect(() => {
    // Disabled auto-generation to prevent unwanted suggestions
    // Only generate when manually triggered
    if (false && autoGenerate && isVisible && content.length > 100) {
      const timeoutId = setTimeout(() => {
        generateSuggestions();
      }, 3000);
      
      return () => clearTimeout(timeoutId);
    }
  }, [content, autoGenerate, isVisible, generateSuggestions]);

  const currentSuggestion = suggestions[currentSuggestionIndex];

  return (
    <>
      {/* Trigger Button */}
      {!isVisible && (
        <Button
          onClick={onToggleVisibility}
          className="fixed right-6 top-1/2 transform -translate-y-1/2 z-40 h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 shadow-lg"
          title="AI Suggestions"
        >
          <Sparkles className="h-5 w-5 text-white" />
        </Button>
      )}

      {/* Main Panel */}
      {isVisible && (
        <div 
          ref={panelRef}
          className={cn(
            "fixed right-6 top-20 z-40 w-96 transition-all duration-300",
            isMinimized ? "h-16" : "h-[calc(100vh-6rem)]",
            className
          )}
        >
          <Card className="h-full bg-white shadow-xl border border-gray-200 flex flex-col">
            {/* Header */}
            <CardHeader className="pb-3 border-b bg-gradient-to-r from-blue-50 to-purple-50">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <Brain className="h-5 w-5 text-blue-600" />
                  AI Suggestions
                </CardTitle>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsMinimized(!isMinimized)}
                    className="h-8 w-8 p-0"
                  >
                    {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onToggleVisibility}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              {!isMinimized && (
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="outline" className="text-xs">
                    {suggestions.length} suggestions
                  </Badge>
                  {isGenerating && (
                    <Badge variant="outline" className="text-xs bg-yellow-100 text-yellow-700">
                      <Loader2 className="h-2 w-2 mr-1 animate-spin" />
                      Generating...
                    </Badge>
                  )}
                </div>
              )}
            </CardHeader>

            {!isMinimized && (
              <CardContent className="flex-1 p-0 flex flex-col overflow-hidden">
                {/* Action Buttons */}
                <div className="p-4 border-b space-y-2">
                  <div className="flex gap-2">
                    <Button
                      onClick={() => generateSuggestions()}
                      disabled={isGenerating}
                      className="flex-1 h-8"
                      size="sm"
                    >
                      <RefreshCw className={cn("h-3 w-3 mr-1", isGenerating && "animate-spin")} />
                      Generate
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setShowCustomPrompt(!showCustomPrompt)}
                      className="h-8"
                      size="sm"
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                  
                  {showCustomPrompt && (
                    <div className="space-y-2">
                      <Textarea
                        value={customPrompt}
                        onChange={(e) => setCustomPrompt(e.target.value)}
                        placeholder="Describe what you want to write about..."
                        className="min-h-[60px] text-sm"
                      />
                      <div className="flex gap-2">
                        <Button
                          onClick={handleCustomPromptSubmit}
                          disabled={!customPrompt.trim() || isGenerating}
                          size="sm"
                          className="flex-1"
                        >
                          <Wand2 className="h-3 w-3 mr-1" />
                          Generate
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => setShowCustomPrompt(false)}
                          size="sm"
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Suggestions Display */}
                <ScrollArea className="flex-1">
                  {suggestions.length === 0 ? (
                    <div className="p-8 text-center text-gray-500">
                      <Brain className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                      <div className="text-sm font-medium mb-1">No suggestions yet</div>
                      <div className="text-xs">Click Generate to get AI-powered paragraph suggestions</div>
                    </div>
                  ) : (
                    <div className="p-4">
                      {currentSuggestion && (
                        <Card className="border">
                          <CardContent className="p-4">
                            {/* Suggestion Header */}
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center gap-2">
                                <Badge 
                                  variant="secondary" 
                                  className={cn(
                                    "text-xs",
                                    currentSuggestion.hasResearch ? 'bg-green-100 text-green-700' :
                                    currentSuggestion.type === 'research' ? 'bg-blue-100 text-blue-700' :
                                    'bg-purple-100 text-purple-700'
                                  )}
                                >
                                  {currentSuggestion.hasResearch && <Search className="h-2 w-2 mr-1" />}
                                  {currentSuggestion.type}
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {currentSuggestion.wordCount} words
                                </span>
                                <span className="text-xs text-gray-500">
                                  {Math.round(currentSuggestion.confidence * 100)}%
                                </span>
                              </div>
                              
                              {suggestions.length > 1 && (
                                <div className="flex gap-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setCurrentSuggestionIndex(Math.max(0, currentSuggestionIndex - 1))}
                                    disabled={currentSuggestionIndex === 0}
                                    className="h-6 w-6 p-0"
                                  >
                                    <ChevronLeft className="h-3 w-3" />
                                  </Button>
                                  <span className="text-xs text-gray-500 px-2 py-1">
                                    {currentSuggestionIndex + 1} of {suggestions.length}
                                  </span>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setCurrentSuggestionIndex(Math.min(suggestions.length - 1, currentSuggestionIndex + 1))}
                                    disabled={currentSuggestionIndex === suggestions.length - 1}
                                    className="h-6 w-6 p-0"
                                  >
                                    <ChevronRight className="h-3 w-3" />
                                  </Button>
                                </div>
                              )}
                            </div>

                            {/* Suggestion Content */}
                            <div className="text-sm text-gray-800 leading-relaxed mb-3 p-3 bg-gray-50 rounded border-l-2 border-blue-400">
                              {currentSuggestion.content}
                            </div>

                            {/* Reasoning */}
                            <div className="text-xs text-gray-500 italic mb-3">
                              {currentSuggestion.reasoning}
                            </div>

                            {/* Citations */}
                            {currentSuggestion.citations && currentSuggestion.citations.length > 0 && (
                              <div className="mb-3 p-2 bg-green-50 rounded border">
                                <div className="flex items-center gap-1 mb-1">
                                  <Quote className="h-3 w-3 text-green-600" />
                                  <span className="text-xs font-medium text-green-800">
                                    {currentSuggestion.citations.length} Research Source(s)
                                  </span>
                                </div>
                                <div className="space-y-1">
                                  {currentSuggestion.citations.map((citation, index) => (
                                    <div key={citation.id} className="text-xs">
                                      <a 
                                        href={citation.url} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:text-blue-800 underline"
                                      >
                                        {citation.title}
                                      </a>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Action Buttons */}
                            <div className="flex gap-2">
                              <Button
                                onClick={() => handleAcceptSuggestion(currentSuggestion)}
                                className="flex-1 h-8 bg-blue-600 hover:bg-blue-700"
                                size="sm"
                              >
                                <Check className="h-3 w-3 mr-1" />
                                Accept
                              </Button>
                              <Button
                                variant="outline"
                                onClick={() => setSuggestions(prev => prev.filter(s => s.id !== currentSuggestion.id))}
                                className="h-8"
                                size="sm"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            )}
          </Card>
        </div>
      )}
    </>
  );
}
