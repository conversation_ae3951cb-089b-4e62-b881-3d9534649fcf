/**
 * Section Citation Integration Service
 * Manages citations and references in section-based writing
 */

import { sectionContextService } from './section-context.service';
import { tavilySearchService } from '../../ai-tutor/services/tavily-search.service';

export interface Citation {
  id: string;
  title: string;
  authors: string[];
  year: number;
  source: string;
  url?: string;
  doi?: string;
  type: 'journal' | 'book' | 'website' | 'conference' | 'other';
  sectionUsed: string[];
}

export interface FormattedCitation {
  inText: string;
  reference: string;
  style: 'APA' | 'MLA' | 'Chicago' | 'IEEE';
}

class SectionCitationService {
  private citations: Map<string, Citation> = new Map();
  private citationCounter = 1;

  /**
   * Add citation from search result
   */
  addCitationFromSearch(searchResult: any, sectionId: string): Citation {
    const citation: Citation = {
      id: `cite_${this.citationCounter++}`,
      title: searchResult.title || 'Untitled',
      authors: this.extractAuthors(searchResult),
      year: this.extractYear(searchResult),
      source: searchResult.url || '',
      url: searchResult.url,
      type: this.determineSourceType(searchResult),
      sectionUsed: [sectionId]
    };

    this.citations.set(citation.id, citation);
    this.updateSectionCitations(sectionId, citation.id);
    
    return citation;
  }

  /**
   * Add manual citation
   */
  addManualCitation(citationData: Partial<Citation>, sectionId: string): Citation {
    const citation: Citation = {
      id: `cite_${this.citationCounter++}`,
      title: citationData.title || 'Untitled',
      authors: citationData.authors || [],
      year: citationData.year || new Date().getFullYear(),
      source: citationData.source || '',
      url: citationData.url,
      doi: citationData.doi,
      type: citationData.type || 'other',
      sectionUsed: [sectionId]
    };

    this.citations.set(citation.id, citation);
    this.updateSectionCitations(sectionId, citation.id);
    
    return citation;
  }

  /**
   * Format citation for in-text use
   */
  formatInTextCitation(citationId: string, style: 'APA' | 'MLA' | 'Chicago' | 'IEEE' = 'APA'): string {
    const citation = this.citations.get(citationId);
    if (!citation) return '';

    switch (style) {
      case 'APA':
        return this.formatAPAInText(citation);
      case 'MLA':
        return this.formatMLAInText(citation);
      case 'Chicago':
        return this.formatChicagoInText(citation);
      case 'IEEE':
        return this.formatIEEEInText(citation);
      default:
        return this.formatAPAInText(citation);
    }
  }

  /**
   * Format citation for reference list
   */
  formatReferenceCitation(citationId: string, style: 'APA' | 'MLA' | 'Chicago' | 'IEEE' = 'APA'): string {
    const citation = this.citations.get(citationId);
    if (!citation) return '';

    switch (style) {
      case 'APA':
        return this.formatAPAReference(citation);
      case 'MLA':
        return this.formatMLAReference(citation);
      case 'Chicago':
        return this.formatChicagoReference(citation);
      case 'IEEE':
        return this.formatIEEEReference(citation);
      default:
        return this.formatAPAReference(citation);
    }
  }

  /**
   * Get all citations used in a section
   */
  getSectionCitations(sectionId: string): Citation[] {
    return Array.from(this.citations.values()).filter(
      citation => citation.sectionUsed.includes(sectionId)
    );
  }

  /**
   * Get all citations for references section
   */
  getAllCitations(): Citation[] {
    return Array.from(this.citations.values());
  }

  /**
   * Generate references section content
   */
  generateReferencesSection(style: 'APA' | 'MLA' | 'Chicago' | 'IEEE' = 'APA'): string {
    const citations = this.getAllCitations();
    if (citations.length === 0) {
      return 'No references found. Add citations to your sections to automatically generate references.';
    }

    // Sort citations alphabetically by first author's last name
    const sortedCitations = citations.sort((a, b) => {
      const aAuthor = a.authors[0] || a.title;
      const bAuthor = b.authors[0] || b.title;
      return aAuthor.localeCompare(bAuthor);
    });

    const formattedReferences = sortedCitations.map(citation => 
      this.formatReferenceCitation(citation.id, style)
    );

    return formattedReferences.join('\n\n');
  }

  /**
   * Extract authors from search result
   */
  private extractAuthors(searchResult: any): string[] {
    // Try to extract authors from various fields
    if (searchResult.authors) return searchResult.authors;
    if (searchResult.author) return [searchResult.author];
    
    // Try to extract from content or title
    const content = searchResult.content || searchResult.title || '';
    const authorMatch = content.match(/(?:by|author[s]?:?)\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/i);
    if (authorMatch) {
      return [authorMatch[1]];
    }

    return ['Unknown Author'];
  }

  /**
   * Extract publication year from search result
   */
  private extractYear(searchResult: any): number {
    if (searchResult.year) return parseInt(searchResult.year);
    if (searchResult.published_date) {
      const year = new Date(searchResult.published_date).getFullYear();
      if (year > 1900 && year <= new Date().getFullYear()) return year;
    }

    // Try to extract year from content
    const content = searchResult.content || searchResult.title || '';
    const yearMatch = content.match(/\b(19|20)\d{2}\b/);
    if (yearMatch) {
      const year = parseInt(yearMatch[0]);
      if (year > 1900 && year <= new Date().getFullYear()) return year;
    }

    return new Date().getFullYear();
  }

  /**
   * Determine source type from search result
   */
  private determineSourceType(searchResult: any): Citation['type'] {
    const url = searchResult.url || '';
    const title = searchResult.title || '';
    
    if (url.includes('doi.org') || title.includes('Journal')) return 'journal';
    if (url.includes('conference') || title.includes('Conference')) return 'conference';
    if (url.includes('book') || title.includes('Book')) return 'book';
    if (url.includes('http')) return 'website';
    
    return 'other';
  }

  /**
   * Update section citations in context
   */
  private updateSectionCitations(sectionId: string, citationId: string) {
    const context = sectionContextService.getContext();
    const section = context.sections[sectionId];
    
    if (section) {
      const updatedCitations = [...section.citations, citationId];
      section.citations = Array.from(new Set(updatedCitations)); // Remove duplicates
    }
  }

  // Citation formatting methods
  private formatAPAInText(citation: Citation): string {
    const author = citation.authors[0] || 'Unknown';
    const lastName = author.split(' ').pop() || author;
    return `(${lastName}, ${citation.year})`;
  }

  private formatMLAInText(citation: Citation): string {
    const author = citation.authors[0] || 'Unknown';
    const lastName = author.split(' ').pop() || author;
    return `(${lastName})`;
  }

  private formatChicagoInText(citation: Citation): string {
    const author = citation.authors[0] || 'Unknown';
    const lastName = author.split(' ').pop() || author;
    return `(${lastName} ${citation.year})`;
  }

  private formatIEEEInText(citation: Citation): string {
    // IEEE uses numbered citations
    const citations = Array.from(this.citations.values());
    const index = citations.findIndex(c => c.id === citation.id) + 1;
    return `[${index}]`;
  }

  private formatAPAReference(citation: Citation): string {
    const authors = citation.authors.join(', ');
    let reference = `${authors} (${citation.year}). ${citation.title}.`;
    
    if (citation.url) {
      reference += ` Retrieved from ${citation.url}`;
    }
    
    return reference;
  }

  private formatMLAReference(citation: Citation): string {
    const authors = citation.authors.join(', ');
    let reference = `${authors}. "${citation.title}."`;
    
    if (citation.url) {
      reference += ` Web. ${new Date().toLocaleDateString()}.`;
    }
    
    return reference;
  }

  private formatChicagoReference(citation: Citation): string {
    const authors = citation.authors.join(', ');
    let reference = `${authors}. "${citation.title}."`;
    
    if (citation.url) {
      reference += ` Accessed ${new Date().toLocaleDateString()}. ${citation.url}.`;
    }
    
    return reference;
  }

  private formatIEEEReference(citation: Citation): string {
    const authors = citation.authors.join(', ');
    let reference = `${authors}, "${citation.title},"`;
    
    if (citation.url) {
      reference += ` [Online]. Available: ${citation.url}`;
    }
    
    return reference;
  }

  /**
   * Clear all citations
   */
  clearAllCitations() {
    this.citations.clear();
    this.citationCounter = 1;
  }

  /**
   * Remove citation
   */
  removeCitation(citationId: string) {
    this.citations.delete(citationId);
  }
}

export const sectionCitationService = new SectionCitationService();
