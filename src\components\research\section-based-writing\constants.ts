/**
 * Constants for Section-Based Research Paper Writing
 */

import { 
  BookOpen, 
  FlaskConical, 
  BarChart3, 
  Lightbulb, 
  Target, 
  FileText, 
  Hash, 
  Quote,
  CheckCircle
} from 'lucide-react';
import { ResearchSection, SectionTemplate, SectionAITool } from './types';

// Research paper sections with enhanced metadata
export const RESEARCH_SECTIONS: ResearchSection[] = [
  {
    id: 'title',
    name: 'Title',
    description: 'Research paper title and basic information',
    icon: FileText,
    color: 'bg-slate-500',
    order: 0,
    required: true,
    estimatedWords: 15,
    aiPromptTemplate: 'Generate a compelling and academically appropriate title for a research paper about: {topic}. The title should be specific, clear, and engaging.',
    searchEnabled: false,
    contextDependencies: []
  },
  {
    id: 'abstract',
    name: 'Abstract',
    description: 'Concise summary of the research',
    icon: Target,
    color: 'bg-indigo-500',
    order: 1,
    required: true,
    estimatedWords: 250,
    aiPromptTemplate: 'Write an academic abstract for a research paper titled "{title}". Include background, methodology, key findings, and implications. Keep it under 250 words.',
    searchEnabled: false,
    contextDependencies: ['title', 'introduction', 'methodology', 'results', 'conclusion']
  },
  {
    id: 'keywords',
    name: 'Keywords',
    description: 'Key terms and phrases',
    icon: Hash,
    color: 'bg-cyan-500',
    order: 2,
    required: true,
    estimatedWords: 20,
    aiPromptTemplate: 'Generate 5-8 relevant keywords for a research paper titled "{title}" in the field of {researchField}. Focus on specific, searchable terms.',
    searchEnabled: false,
    contextDependencies: ['title', 'abstract']
  },
  {
    id: 'introduction',
    name: 'Introduction',
    description: 'Background, problem statement, and objectives',
    icon: BookOpen,
    color: 'bg-purple-500',
    order: 3,
    required: true,
    estimatedWords: 800,
    aiPromptTemplate: 'Write a comprehensive introduction for a research paper titled "{title}". Include background information, literature review, problem statement, research questions, and objectives. Use current academic sources and proper citations.',
    searchEnabled: true,
    contextDependencies: ['title', 'abstract']
  },
  {
    id: 'methodology',
    name: 'Methodology',
    description: 'Research methods and procedures',
    icon: FlaskConical,
    color: 'bg-blue-500',
    order: 4,
    required: true,
    estimatedWords: 600,
    aiPromptTemplate: 'Write a detailed methodology section for the research paper "{title}". Describe the research design, data collection methods, sample size, analysis techniques, and ethical considerations. Reference the research objectives from the introduction.',
    searchEnabled: true,
    contextDependencies: ['introduction']
  },
  {
    id: 'results',
    name: 'Results',
    description: 'Findings and data analysis',
    icon: BarChart3,
    color: 'bg-green-500',
    order: 5,
    required: true,
    estimatedWords: 700,
    aiPromptTemplate: 'Write a results section for the research paper "{title}". Present findings clearly and objectively, reference the methodology used, and include data analysis. Focus on what was found, not interpretation.',
    searchEnabled: false,
    contextDependencies: ['methodology']
  },
  {
    id: 'discussion',
    name: 'Discussion',
    description: 'Interpretation of results and implications',
    icon: Lightbulb,
    color: 'bg-yellow-500',
    order: 6,
    required: true,
    estimatedWords: 800,
    aiPromptTemplate: 'Write a discussion section for the research paper "{title}". Interpret the results, compare with existing literature, discuss implications, limitations, and future research directions. Reference both the results and introduction sections.',
    searchEnabled: true,
    contextDependencies: ['results', 'introduction']
  },
  {
    id: 'conclusion',
    name: 'Conclusion',
    description: 'Summary and final thoughts',
    icon: CheckCircle,
    color: 'bg-emerald-500',
    order: 7,
    required: true,
    estimatedWords: 300,
    aiPromptTemplate: 'Write a conclusion for the research paper "{title}". Summarize key findings, restate the significance, and provide final thoughts. Reference the main points from discussion and results.',
    searchEnabled: false,
    contextDependencies: ['discussion', 'results', 'introduction']
  },
  {
    id: 'references',
    name: 'References',
    description: 'Bibliography and citations',
    icon: Quote,
    color: 'bg-gray-500',
    order: 8,
    required: true,
    estimatedWords: 200,
    aiPromptTemplate: 'Generate a properly formatted references section for the research paper "{title}". Include all citations used throughout the paper in {citationStyle} format.',
    searchEnabled: false,
    contextDependencies: ['introduction', 'methodology', 'discussion']
  }
];

// Section-specific AI tools
export const SECTION_AI_TOOLS: SectionAITool[] = [
  {
    id: 'generate-section',
    name: 'Generate Section',
    description: 'AI-generate the entire section content',
    icon: 'Wand2',
    applicableSections: ['introduction', 'methodology', 'results', 'discussion', 'conclusion'],
    requiresSearch: true,
    mode: 'generate',
    promptTemplate: (context) => {
      const { currentSection, previousSections, paperMetadata } = context;
      const contextText = Object.entries(previousSections)
        .map(([id, content]) => `${id.toUpperCase()}:\n${content}`)
        .join('\n\n');
      
      return `${currentSection.aiPromptTemplate
        .replace('{title}', paperMetadata.title)
        .replace('{researchField}', paperMetadata.researchField)}
        
Previous sections for context:
${contextText}

Generate comprehensive, academic content for this section.`;
    }
  },
  {
    id: 'enhance-with-citations',
    name: 'Add Citations',
    description: 'Enhance content with academic citations',
    icon: 'Quote',
    applicableSections: ['introduction', 'methodology', 'discussion'],
    requiresSearch: true,
    mode: 'enhance',
    promptTemplate: (context) => 
      `Enhance the following text with relevant academic citations and references. Use current research and proper citation format:

${context.sectionContent}

Search results for reference:
${context.searchResults?.map(r => `${r.title}: ${r.content}`).join('\n\n') || 'No search results available'}`
  },
  {
    id: 'improve-academic-tone',
    name: 'Academic Tone',
    description: 'Improve academic writing style',
    icon: 'PenTool',
    applicableSections: ['introduction', 'methodology', 'results', 'discussion', 'conclusion'],
    requiresSearch: false,
    mode: 'enhance',
    promptTemplate: (context) => 
      `Improve the academic tone and style of the following text. Make it more formal, precise, and scholarly while maintaining clarity:

${context.sectionContent}`
  },
  {
    id: 'expand-section',
    name: 'Expand Content',
    description: 'Expand and elaborate on the content',
    icon: 'TrendingUp',
    applicableSections: ['introduction', 'methodology', 'discussion'],
    requiresSearch: true,
    mode: 'enhance',
    promptTemplate: (context) => 
      `Expand and elaborate on the following section content. Add more detail, examples, and depth while maintaining academic rigor:

${context.sectionContent}

Context from other sections:
${Object.entries(context.previousSections).map(([id, content]) => `${id}: ${content.slice(0, 200)}...`).join('\n')}`
  },
  {
    id: 'check-structure',
    name: 'Check Structure',
    description: 'Analyze and improve section structure',
    icon: 'Layers',
    applicableSections: ['introduction', 'methodology', 'discussion'],
    requiresSearch: false,
    mode: 'enhance',
    promptTemplate: (context) => 
      `Analyze the structure of this ${context.currentSection.name} section and suggest improvements. Check for logical flow, completeness, and academic standards:

${context.sectionContent}`
  }
];

// Default research paper template
export const DEFAULT_RESEARCH_TEMPLATE: SectionTemplate = {
  id: 'standard-research-paper',
  name: 'Standard Research Paper',
  description: 'Traditional academic research paper structure',
  sections: RESEARCH_SECTIONS,
  defaultMetadata: {
    title: '',
    abstract: '',
    keywords: [],
    researchField: '',
    authors: []
  },
  aiModelRecommendations: {
    'introduction': 'google/gemini-2.5-flash',
    'methodology': 'google/gemini-2.5-flash', 
    'results': 'google/gemini-2.5-pro',
    'discussion': 'google/gemini-2.5-pro',
    'conclusion': 'google/gemini-2.5-flash',
    'abstract': 'google/gemini-2.5-flash'
  }
};

// Word count targets by section
export const SECTION_WORD_TARGETS = {
  title: { min: 5, max: 20, target: 15 },
  abstract: { min: 150, max: 300, target: 250 },
  keywords: { min: 5, max: 30, target: 20 },
  introduction: { min: 500, max: 1200, target: 800 },
  methodology: { min: 400, max: 800, target: 600 },
  results: { min: 500, max: 1000, target: 700 },
  discussion: { min: 600, max: 1200, target: 800 },
  conclusion: { min: 200, max: 400, target: 300 },
  references: { min: 100, max: 500, target: 200 }
};

// Citation styles supported
export const CITATION_STYLES = [
  { id: 'APA', name: 'APA (7th Edition)', description: 'American Psychological Association' },
  { id: 'MLA', name: 'MLA (9th Edition)', description: 'Modern Language Association' },
  { id: 'Chicago', name: 'Chicago (17th Edition)', description: 'Chicago Manual of Style' },
  { id: 'IEEE', name: 'IEEE', description: 'Institute of Electrical and Electronics Engineers' }
];
