/**
 * Section AI Controls Component
 * Provides user controls for AI features in section-based writing
 */

import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  Zap, 
  Settings, 
  ToggleLeft, 
  ToggleRight,
  Sparkles,
  Clock,
  Target,
  RefreshCw
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { toast } from 'sonner';

import { sectionAIConfigService, SectionAIConfig } from '../services/section-ai-config.service';
import { sectionContextService } from '../services/section-context.service';

interface SectionAIControlsProps {
  onConfigChange?: (config: SectionAIConfig) => void;
  className?: string;
}

export function SectionAIControls({
  onConfigChange,
  className = ''
}: SectionAIControlsProps) {
  const [config, setConfig] = useState<SectionAIConfig>(sectionAIConfigService.getCurrentSectionConfig());
  const [preferences, setPreferences] = useState(sectionAIConfigService.getPreferences());
  const [suggestionsTriggered, setSuggestionsTriggered] = useState(false);

  useEffect(() => {
    const unsubscribe = sectionContextService.subscribe(() => {
      const newConfig = sectionAIConfigService.getCurrentSectionConfig();
      setConfig(newConfig);
      onConfigChange?.(newConfig);
    });

    return unsubscribe;
  }, [onConfigChange]);

  const handleAutocompletionToggle = () => {
    sectionAIConfigService.toggleAutocompletion();
    const newPreferences = sectionAIConfigService.getPreferences();
    setPreferences(newPreferences);
    
    const newConfig = sectionAIConfigService.getCurrentSectionConfig();
    setConfig(newConfig);
    onConfigChange?.(newConfig);
    
    toast.success(`Autocompletion ${newPreferences.autocompletionEnabled ? 'enabled' : 'disabled'}`);
  };

  const handleSuggestionModeChange = (mode: 'off' | 'ondemand' | 'always') => {
    switch (mode) {
      case 'off':
        sectionAIConfigService.disableAllSuggestions();
        break;
      case 'ondemand':
        sectionAIConfigService.enableSuggestionsOnDemand();
        break;
      case 'always':
        sectionAIConfigService.enableAlwaysSuggestions();
        break;
    }

    const newPreferences = sectionAIConfigService.getPreferences();
    setPreferences(newPreferences);
    
    const newConfig = sectionAIConfigService.getCurrentSectionConfig();
    setConfig(newConfig);
    onConfigChange?.(newConfig);
    
    toast.success(`Suggestions mode: ${mode}`);
  };

  const handleTriggerSuggestions = () => {
    setSuggestionsTriggered(true);
    toast.info('AI suggestions triggered for current context');
    
    // Reset after a short delay
    setTimeout(() => {
      setSuggestionsTriggered(false);
    }, 2000);
  };

  const getSuggestionMode = () => {
    if (preferences.alwaysShowSuggestions) return 'always';
    if (preferences.suggestionsOnDemand) return 'ondemand';
    return 'off';
  };

  const context = sectionContextService.getContext();
  const isTemplateMode = context.writingMode === 'template';

  if (!isTemplateMode) {
    return null;
  }

  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-sm">
          <Bot className="h-4 w-4 text-blue-600" />
          <span>AI Writing Controls</span>
          <Badge variant="outline" className="text-xs">
            {context.currentSection || 'No section'}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Autocompletion Control */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Zap className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium">Autocompletion</span>
          </div>
          <Switch
            checked={preferences.autocompletionEnabled}
            onCheckedChange={handleAutocompletionToggle}
          />
        </div>

        {/* Suggestions Control */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Sparkles className="h-4 w-4 text-purple-600" />
            <span className="text-sm font-medium">AI Suggestions</span>
          </div>
          
          <Select value={getSuggestionMode()} onValueChange={handleSuggestionModeChange}>
            <SelectTrigger className="h-8 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="off" className="text-xs">
                <div className="flex items-center space-x-2">
                  <ToggleLeft className="h-3 w-3" />
                  <span>Disabled</span>
                </div>
              </SelectItem>
              <SelectItem value="ondemand" className="text-xs">
                <div className="flex items-center space-x-2">
                  <Target className="h-3 w-3" />
                  <span>On Demand (Click Auto)</span>
                </div>
              </SelectItem>
              <SelectItem value="always" className="text-xs">
                <div className="flex items-center space-x-2">
                  <ToggleRight className="h-3 w-3" />
                  <span>Always Active</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>

          {/* Manual Trigger Button */}
          {getSuggestionMode() === 'ondemand' && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleTriggerSuggestions}
              disabled={suggestionsTriggered}
              className="w-full h-8 text-xs"
            >
              {suggestionsTriggered ? (
                <>
                  <RefreshCw className="h-3 w-3 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="h-3 w-3 mr-2" />
                  Trigger AI Suggestions
                </>
              )}
            </Button>
          )}
        </div>

        {/* Current Section Config */}
        <div className="pt-3 border-t border-gray-200">
          <h4 className="text-xs font-medium text-gray-700 mb-2">Current Section Settings</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Max Suggestions:</span>
              <Badge variant="outline" className="text-xs">
                {config.maxSuggestions}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Response Time:</span>
              <Badge variant="outline" className="text-xs">
                {config.responseTime}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Research:</span>
              <Badge 
                variant="outline" 
                className={`text-xs ${config.enableResearch ? 'border-green-200 text-green-700' : 'border-gray-200 text-gray-500'}`}
              >
                {config.enableResearch ? 'Enabled' : 'Disabled'}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Context:</span>
              <Badge variant="outline" className="text-xs">
                {config.contextDepth}
              </Badge>
            </div>
          </div>
        </div>

        {/* Section Prompts */}
        {context.currentSection && (
          <div className="pt-3 border-t border-gray-200">
            <h4 className="text-xs font-medium text-gray-700 mb-2">Writing Tips</h4>
            <div className="space-y-1">
              {sectionAIConfigService.getSectionPrompts(context.currentSection).slice(0, 2).map((prompt, index) => (
                <div key={index} className="text-xs text-gray-600 flex items-start space-x-1">
                  <span className="text-blue-500 mt-0.5">•</span>
                  <span>{prompt}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="pt-3 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 text-xs"
                    onClick={() => {
                      const newConfig = sectionAIConfigService.getCurrentSectionConfig();
                      setConfig(newConfig);
                      onConfigChange?.(newConfig);
                      toast.success('Configuration refreshed');
                    }}
                  >
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Refresh
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">Refresh AI configuration</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 text-xs"
                    onClick={() => {
                      // TODO: Open detailed settings
                      toast.info('Advanced settings coming soon');
                    }}
                  >
                    <Settings className="h-3 w-3 mr-1" />
                    Settings
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">Advanced AI settings</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
