/**
 * Section-Aware AI Configuration Service
 * Configures AI features based on current section context
 */

import { sectionContextService } from './section-context.service';
import { RESEARCH_SECTIONS } from '../constants';

export interface SectionAIConfig {
  enableAutocompletion: boolean;
  enableSuggestions: boolean;
  enableInlineSuggestions: boolean;
  citationStyle: 'APA' | 'MLA' | 'Chicago' | 'IEEE';
  maxSuggestions: number;
  responseTime: 'fast' | 'balanced' | 'thorough';
  enableResearch: boolean;
  contextDepth: 'minimal' | 'moderate' | 'comprehensive';
}

class SectionAIConfigService {
  private userPreferences = {
    autocompletionEnabled: true,
    suggestionsOnDemand: true, // Only when user clicks "auto" button
    alwaysShowSuggestions: false
  };

  /**
   * Get AI configuration for current section
   */
  getCurrentSectionConfig(): SectionAIConfig {
    const context = sectionContextService.getContext();
    const currentSection = RESEARCH_SECTIONS.find(s => s.id === context.currentSection);
    
    if (!currentSection || context.writingMode !== 'template') {
      return this.getDefaultConfig();
    }

    return this.getSectionSpecificConfig(currentSection.id);
  }

  /**
   * Get section-specific AI configuration
   */
  private getSectionSpecificConfig(sectionId: string): SectionAIConfig {
    const baseConfig = this.getDefaultConfig();

    switch (sectionId) {
      case 'title':
        return {
          ...baseConfig,
          enableAutocompletion: true,
          enableSuggestions: this.userPreferences.suggestionsOnDemand,
          maxSuggestions: 3,
          responseTime: 'fast',
          enableResearch: false,
          contextDepth: 'minimal'
        };

      case 'abstract':
        return {
          ...baseConfig,
          enableAutocompletion: true,
          enableSuggestions: this.userPreferences.suggestionsOnDemand,
          maxSuggestions: 2,
          responseTime: 'balanced',
          enableResearch: false,
          contextDepth: 'moderate'
        };

      case 'keywords':
        return {
          ...baseConfig,
          enableAutocompletion: true,
          enableSuggestions: this.userPreferences.suggestionsOnDemand,
          maxSuggestions: 5,
          responseTime: 'fast',
          enableResearch: true,
          contextDepth: 'minimal'
        };

      case 'introduction':
        return {
          ...baseConfig,
          enableAutocompletion: true,
          enableSuggestions: this.userPreferences.suggestionsOnDemand,
          maxSuggestions: 2,
          responseTime: 'thorough',
          enableResearch: true,
          contextDepth: 'comprehensive'
        };

      case 'methodology':
        return {
          ...baseConfig,
          enableAutocompletion: true,
          enableSuggestions: this.userPreferences.suggestionsOnDemand,
          maxSuggestions: 2,
          responseTime: 'thorough',
          enableResearch: true,
          contextDepth: 'comprehensive'
        };

      case 'results':
        return {
          ...baseConfig,
          enableAutocompletion: true,
          enableSuggestions: this.userPreferences.suggestionsOnDemand,
          maxSuggestions: 1,
          responseTime: 'balanced',
          enableResearch: false,
          contextDepth: 'moderate'
        };

      case 'discussion':
        return {
          ...baseConfig,
          enableAutocompletion: true,
          enableSuggestions: this.userPreferences.suggestionsOnDemand,
          maxSuggestions: 2,
          responseTime: 'thorough',
          enableResearch: true,
          contextDepth: 'comprehensive'
        };

      case 'conclusion':
        return {
          ...baseConfig,
          enableAutocompletion: true,
          enableSuggestions: this.userPreferences.suggestionsOnDemand,
          maxSuggestions: 1,
          responseTime: 'balanced',
          enableResearch: false,
          contextDepth: 'comprehensive'
        };

      case 'references':
        return {
          ...baseConfig,
          enableAutocompletion: false,
          enableSuggestions: false,
          maxSuggestions: 0,
          responseTime: 'fast',
          enableResearch: false,
          contextDepth: 'minimal'
        };

      default:
        return baseConfig;
    }
  }

  /**
   * Get default AI configuration
   */
  private getDefaultConfig(): SectionAIConfig {
    return {
      enableAutocompletion: this.userPreferences.autocompletionEnabled,
      enableSuggestions: this.userPreferences.alwaysShowSuggestions,
      enableInlineSuggestions: true,
      citationStyle: 'APA',
      maxSuggestions: 2,
      responseTime: 'balanced',
      enableResearch: true,
      contextDepth: 'moderate'
    };
  }

  /**
   * Update user preferences
   */
  updatePreferences(preferences: Partial<typeof this.userPreferences>) {
    this.userPreferences = { ...this.userPreferences, ...preferences };
  }

  /**
   * Get user preferences
   */
  getPreferences() {
    return { ...this.userPreferences };
  }

  /**
   * Enable suggestions on demand (when user clicks auto button)
   */
  enableSuggestionsOnDemand() {
    this.userPreferences.suggestionsOnDemand = true;
    this.userPreferences.alwaysShowSuggestions = false;
  }

  /**
   * Enable always-on suggestions
   */
  enableAlwaysSuggestions() {
    this.userPreferences.alwaysShowSuggestions = true;
    this.userPreferences.suggestionsOnDemand = false;
  }

  /**
   * Disable all suggestions
   */
  disableAllSuggestions() {
    this.userPreferences.suggestionsOnDemand = false;
    this.userPreferences.alwaysShowSuggestions = false;
  }

  /**
   * Toggle autocompletion
   */
  toggleAutocompletion() {
    this.userPreferences.autocompletionEnabled = !this.userPreferences.autocompletionEnabled;
  }

  /**
   * Get section context for AI prompts
   */
  getSectionContext(): string {
    const context = sectionContextService.getContext();
    const currentSection = RESEARCH_SECTIONS.find(s => s.id === context.currentSection);
    
    if (!currentSection) return '';

    let contextText = `Writing ${currentSection.name} section for: "${context.paperMetadata.title}"\n`;
    
    if (context.paperMetadata.researchField) {
      contextText += `Research field: ${context.paperMetadata.researchField}\n`;
    }

    // Add context from previous sections
    const dependencies = currentSection.contextDependencies;
    if (dependencies.length > 0) {
      contextText += '\nPrevious sections:\n';
      dependencies.forEach(depId => {
        const depContent = context.sections[depId]?.content;
        if (depContent) {
          contextText += `${depId}: ${depContent.slice(0, 200)}...\n`;
        }
      });
    }

    return contextText;
  }

  /**
   * Get section-specific writing prompts
   */
  getSectionPrompts(sectionId: string): string[] {
    switch (sectionId) {
      case 'title':
        return [
          'Create a compelling research title that clearly states the main focus',
          'Ensure the title is specific and academically appropriate',
          'Consider including key variables or methodology if relevant'
        ];

      case 'abstract':
        return [
          'Summarize the research problem and objectives',
          'Briefly describe the methodology used',
          'Highlight key findings and implications',
          'Keep within 250 words'
        ];

      case 'introduction':
        return [
          'Provide background context for the research',
          'Review relevant literature and identify gaps',
          'State the research problem clearly',
          'Present research questions and objectives'
        ];

      case 'methodology':
        return [
          'Describe the research design and approach',
          'Explain data collection methods',
          'Detail analysis procedures',
          'Address ethical considerations'
        ];

      case 'results':
        return [
          'Present findings objectively without interpretation',
          'Use appropriate tables and figures',
          'Report statistical results clearly',
          'Organize results logically'
        ];

      case 'discussion':
        return [
          'Interpret the results in context of existing literature',
          'Discuss implications of findings',
          'Address limitations of the study',
          'Suggest directions for future research'
        ];

      case 'conclusion':
        return [
          'Summarize key findings',
          'Restate the significance of the research',
          'Provide final thoughts and recommendations'
        ];

      default:
        return [];
    }
  }
}

export const sectionAIConfigService = new SectionAIConfigService();
