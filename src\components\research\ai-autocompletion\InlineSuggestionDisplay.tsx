/**
 * Inline Suggestion Display Component
 * Shows AI suggestions directly in the editor with visual feedback and controls
 */

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { 
  Check, 
  X, 
  RefreshCw, 
  Search, 
  Quote,
  Sparkles,
  Loader2,
  ChevronRight,
  ChevronLeft,
  Keyboard
} from 'lucide-react';
import { AutocompletionSuggestion } from './ai-autocompletion.service';
import { cn } from '@/lib/utils';

interface InlineSuggestionDisplayProps {
  suggestions: AutocompletionSuggestion[];
  isGenerating: boolean;
  onAcceptSuggestion: (suggestion: AutocompletionSuggestion) => void;
  onRejectSuggestion: (suggestionId: string) => void;
  onRegenerateSuggestions: () => void;
  className?: string;
  position: { x: number; y: number };
  isVisible: boolean;
}

export function InlineSuggestionDisplay({
  suggestions,
  isGenerating,
  onAcceptSuggestion,
  onRejectSuggestion,
  onRegenerateSuggestions,
  className = '',
  position,
  isVisible
}: InlineSuggestionDisplayProps) {
  const [currentSuggestionIndex, setCurrentSuggestionIndex] = useState(0);
  const [showPreview, setShowPreview] = useState(true);
  const [animationClass, setAnimationClass] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);

  // Reset index when suggestions change
  useEffect(() => {
    setCurrentSuggestionIndex(0);
  }, [suggestions]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isVisible || suggestions.length === 0) return;

      switch (event.key) {
        case 'Tab':
          event.preventDefault();
          if (suggestions[currentSuggestionIndex]) {
            handleAcceptSuggestion(suggestions[currentSuggestionIndex]);
          }
          break;
        case 'Escape':
          event.preventDefault();
          if (suggestions[currentSuggestionIndex]) {
            handleRejectSuggestion(suggestions[currentSuggestionIndex].id);
          }
          break;
        case 'ArrowRight':
          if (event.ctrlKey && suggestions.length > 1) {
            event.preventDefault();
            setCurrentSuggestionIndex((prev) => 
              prev < suggestions.length - 1 ? prev + 1 : 0
            );
          }
          break;
        case 'ArrowLeft':
          if (event.ctrlKey && suggestions.length > 1) {
            event.preventDefault();
            setCurrentSuggestionIndex((prev) => 
              prev > 0 ? prev - 1 : suggestions.length - 1
            );
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isVisible, suggestions, currentSuggestionIndex]);

  // Animation effects
  useEffect(() => {
    if (isVisible && suggestions.length > 0) {
      setAnimationClass('animate-in');
      const timer = setTimeout(() => setAnimationClass(''), 300);
      return () => clearTimeout(timer);
    }
  }, [isVisible, suggestions]);

  const handleAcceptSuggestion = (suggestion: AutocompletionSuggestion) => {
    setAnimationClass('animate-accept');
    setTimeout(() => {
      onAcceptSuggestion(suggestion);
      setAnimationClass('');
    }, 200);
  };

  const handleRejectSuggestion = (suggestionId: string) => {
    setAnimationClass('animate-reject');
    setTimeout(() => {
      onRejectSuggestion(suggestionId);
      setAnimationClass('');
    }, 200);
  };

  const handleNextSuggestion = () => {
    setCurrentSuggestionIndex((prev) => 
      prev < suggestions.length - 1 ? prev + 1 : 0
    );
  };

  const handlePreviousSuggestion = () => {
    setCurrentSuggestionIndex((prev) => 
      prev > 0 ? prev - 1 : suggestions.length - 1
    );
  };

  if (!isVisible) return null;

  const currentSuggestion = suggestions[currentSuggestionIndex];

  return (
    <div
      ref={containerRef}
      className={cn(
        "fixed z-50 transition-all duration-300",
        animationClass === 'animate-in' && "animate-in slide-in-from-bottom-2",
        animationClass === 'animate-accept' && "animate-out slide-out-to-right-2",
        animationClass === 'animate-reject' && "animate-out slide-out-to-left-2",
        className
      )}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: 'translateY(-100%)'
      }}
    >
      {/* Loading State */}
      {isGenerating && (
        <Card className="p-3 bg-white/95 backdrop-blur-sm border border-blue-200 shadow-lg">
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
            <span className="text-sm text-gray-600">Generating suggestions...</span>
          </div>
        </Card>
      )}

      {/* Suggestion Display */}
      {!isGenerating && currentSuggestion && (
        <Card className="bg-white/95 backdrop-blur-sm border border-gray-200 shadow-xl max-w-md">
          {/* Header */}
          <div className="px-3 py-2 border-b bg-gradient-to-r from-blue-50 to-purple-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-800">AI Suggestion</span>
                {suggestions.length > 1 && (
                  <Badge variant="outline" className="text-xs">
                    {currentSuggestionIndex + 1} of {suggestions.length}
                  </Badge>
                )}
              </div>
              
              {/* Navigation for multiple suggestions */}
              {suggestions.length > 1 && (
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handlePreviousSuggestion}
                    className="h-6 w-6 p-0"
                  >
                    <ChevronLeft className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleNextSuggestion}
                    className="h-6 w-6 p-0"
                  >
                    <ChevronRight className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Suggestion Content */}
          <div className="p-3">
            {/* Suggestion Type and Metadata */}
            <div className="flex items-center gap-2 mb-2">
              <Badge 
                variant="secondary" 
                className={cn(
                  "text-xs",
                  currentSuggestion.hasResearch ? 'bg-green-100 text-green-700' :
                  currentSuggestion.type === 'sentence' ? 'bg-blue-100 text-blue-700' :
                  'bg-purple-100 text-purple-700'
                )}
              >
                {currentSuggestion.hasResearch && <Search className="h-2 w-2 mr-1" />}
                {currentSuggestion.type}
              </Badge>
              <span className="text-xs text-gray-500">
                {currentSuggestion.wordCount} words
              </span>
              <span className="text-xs text-gray-500">
                {Math.round(currentSuggestion.confidence * 100)}%
              </span>
            </div>

            {/* Preview Text */}
            {showPreview && (
              <div className="mb-3">
                <div className="text-sm text-gray-800 leading-relaxed p-2 bg-gray-50 rounded border-l-2 border-blue-400">
                  {currentSuggestion.content}
                </div>
                {currentSuggestion.reasoning && (
                  <div className="text-xs text-gray-500 italic mt-1">
                    {currentSuggestion.reasoning}
                  </div>
                )}
              </div>
            )}

            {/* Citations */}
            {currentSuggestion.citations && currentSuggestion.citations.length > 0 && (
              <div className="mb-3 p-2 bg-green-50 rounded border">
                <div className="flex items-center gap-1 mb-1">
                  <Quote className="h-3 w-3 text-green-600" />
                  <span className="text-xs font-medium text-green-800">
                    {currentSuggestion.citations.length} Research Source(s)
                  </span>
                </div>
                <div className="space-y-1">
                  {currentSuggestion.citations.map((citation, index) => (
                    <div key={citation.id} className="text-xs">
                      <a 
                        href={citation.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 underline"
                      >
                        {citation.title}
                      </a>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex items-center justify-between">
              <div className="flex gap-2">
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => handleAcceptSuggestion(currentSuggestion)}
                  className="h-7 px-3 text-xs bg-blue-600 hover:bg-blue-700"
                >
                  <Check className="h-3 w-3 mr-1" />
                  Accept
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleRejectSuggestion(currentSuggestion.id)}
                  className="h-7 px-3 text-xs"
                >
                  <X className="h-3 w-3 mr-1" />
                  Reject
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRegenerateSuggestions}
                  className="h-7 px-3 text-xs"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Regenerate
                </Button>
              </div>

              {/* Keyboard Shortcuts Hint */}
              <div className="flex items-center gap-1 text-xs text-gray-400">
                <Keyboard className="h-3 w-3" />
                <span>Tab/Esc</span>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* No Suggestions State */}
      {!isGenerating && suggestions.length === 0 && (
        <Card className="p-3 bg-white/95 backdrop-blur-sm border border-gray-200 shadow-lg">
          <div className="flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">No suggestions available</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onRegenerateSuggestions}
              className="h-6 px-2 text-xs"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}

// CSS for animations (to be added to global styles)
export const inlineSuggestionStyles = `
  @keyframes animate-in {
    from {
      opacity: 0;
      transform: translateY(-100%) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(-100%) scale(1);
    }
  }

  @keyframes animate-accept {
    from {
      opacity: 1;
      transform: translateY(-100%) scale(1);
    }
    to {
      opacity: 0;
      transform: translateY(-100%) scale(1.05) translateX(20px);
    }
  }

  @keyframes animate-reject {
    from {
      opacity: 1;
      transform: translateY(-100%) scale(1);
    }
    to {
      opacity: 0;
      transform: translateY(-100%) scale(0.95) translateX(-20px);
    }
  }

  .animate-in {
    animation: animate-in 0.3s ease-out;
  }

  .animate-accept {
    animation: animate-accept 0.2s ease-in;
  }

  .animate-reject {
    animation: animate-reject 0.2s ease-in;
  }
`;
